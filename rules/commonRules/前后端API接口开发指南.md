# 目标：
根据用户的需求快速创建API，注意前端不能直接调用RPC,必须通过API包装RPC供前端调用
# 方法：
- 1.参考export const libraAttribution = Api(
  Post('/libra_attribution'),
  Data(
    z.object({
      dids: z.string().optional(),
      appid: z.number().optional(),
      isOverseas: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    reportTeaEvent({
      data: {
        eventName: 'Quality_Click',
        params: {
          toolName: '实验归因',
        },
      },
    });
    const libraHitResults: LibraHitResult[] = [];
    if (data.dids && data.dids.split(',').length <= 5) {
      const insufficientResult: LibraHitResult = { error: 'did过少，为了确保归因准确性，请输入至少10个did' };
      libraHitResults.push(insufficientResult);
      return libraHitResults;
    }
- 2.判断是quality还是host的API，如果不清楚，可以询问用户
- 3.如果是host,需要在创建之后在 apps/host/api/utils/skipLoginPath.ts中添加 /api/+path
- 4.前端访问
- <Button
        onClick={() => {
          setSpinning(true);
          libraAttribution({ data: { dids: didInputRef.current?.value } }).then(flight => {
            console.log('libraAttribution', flight);
            setSpinning(false);
            if (flight) {
              setResult(flight);
            }
          });
        }}
      >
        查询
      </Button>