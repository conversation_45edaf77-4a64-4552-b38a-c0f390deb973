# 飞书通知卡片开发指南
## 目标：
纸飞机项目中开发飞书通知卡片，功能包含飞书卡片的创建，发送，回调，以及回调中更新卡片
## 方法：
必须通过参考代码去进行开发
- 构建：参考：
  export class MsgGrayVersionEnd implements MsgTemplate {
  name = '灰度版本结束通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(info: LibraBaseInfo, region: LibraRegion, isClose: boolean, isSuccess: boolean) {
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];

    // 实验基本信息
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**灰度实验ID**: ${info.flightId}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验名称**: [${info.flightName}](${info.flightUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验状态**: 灰度结束`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    // 实验链接
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验链接**: [${info.flightUrl}](${info.flightUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 实验Owner信息
    let owners = '';
    if (!isWebLibra(info)) {
      for (const owner of info.flightOwners) {
        owners = `${owners}<at email=${owner.email}></at>`;
      }
      elements.push({
        tag: CardElementTagV2.div,
        text: {
          content: `**实验Owner**: ${owners}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }

    // 操作按钮
    // 操作按钮 - 根据状态显示不同按钮
    const cardButtonActions: (CardButtonAction | CardMarkdownElement)[] = [];

    if (!isClose) {
      // 未关闭状态显示关闭按钮
      cardButtonActions.push({
        tag: CardElementTagV2.button,
        text: {
          tag: CardTextTag.plain_text,
          content: '关闭灰度实验',
        },
        type: CardButtonType.danger,
        value: {
          cardCallbackType: CardCallbackType.LibraCloseGray,
          libraInfo: info,
          operator: 'nieweishan',
          region,
        } as unknown as CardActionValue,
        confirm: {
          title: {
            tag: CardTextTag.plain_text,
            content: '确认关闭灰度实验',
          },
          text: {
            tag: CardTextTag.plain_text,
            content: '确定要关闭这个灰度实验吗？此操作不可撤销。',
          },
        },
      } as CardButtonAction);
    } else {
      // 已关闭状态显示结果按钮

      content: cardButtonActions.push({
        tag: CardElementTagV2.markdown,
        content: isSuccess
          ? `<font color="green">✅ 实验关闭成功</font>` // size="4" 增大字体
          : `<font color="red">❌ 实验关闭失败</font>`, // 统一设置字体大小
        text_size: 'heading',
      } as CardMarkdownElement);
    }

    // 创建正式实验按钮
    cardButtonActions.push({
      tag: CardElementTagV2.button,
      url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=1000&url=${encodeURIComponent(`https://pa.bytedance.net/libra/create?appid=177501&version=15.3.0&flightId=${info.flightId}`)}`,
      text: {
        tag: CardTextTag.plain_text,
        content: '创建正式实验',
      },
      type: CardButtonType.primary,
    } as CardButtonAction);

    // 按钮布局
    const buttonColumns: CardColumnElement[] = cardButtonActions.map(
      buttonAction =>
        ({
          tag: CardElementTagV2.column,
          width: 'auto',
          weight: 1,
          vertical_align: 'top',
          elements: [buttonAction],
        }) as CardColumnElement,
    );

    elements.push({
      tag: CardElementTagV2.columnSet,
      flex_mode: 'flow',
      background_style: 'default',
      columns: buttonColumns,
    } as CardColumnSetElement);

    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

- 发送： 参考 await useInject(MessageService).sendNormalMsg(card, UserIdType.openId, 'ou_288568c940e05d5a96e07227d7103b6a');//发送可以发送给个人或者群聊
- 回调：回调操作是某个Active在卡片发送之后在飞书上操作而发生的回调，需要在卡片构建时，对需要回调的active进行设计回调方法，参考上面中的最后一个关闭灰度实验卡片通知的关闭按钮；
- 回调方法设置步骤：1.在packages/shared/dist/src/lark/larkCard.d.ts枚举中加入新的方法 2，在这个中添加调用的方法apps/host/api/service/CardCallbackHandler.ts 3.在新的文件或者在用户指定位置实现调用的方法，可以参考 apps/host/api/service/handler/larkCard/experimentUpdate.ts
- 更新卡片：已经发生的卡片，调用某个active回调后需要更新卡片；方法是首先构建卡片，然后通过参考await useInject(LarkService).updateCard(JSON.stringify(msg), messageId);进行发送更新