### 库导入
在最上面导入库，避免在代码中随机导入
#### 1. Monorepo 共享模块 (`@shared`)

`@shared` 是一个路径别名，指向 `packages/shared/src` 目录，用于存放 `host` 和 `quality` 两个应用需要共用的代码

示例：import { LibraNewInfo } from '@shared/libra/LibraNewInfo';

#### 2. 应用内部模块 (`@/`)

`@/` 是一个路径别名，指向当前应用的 `src` 目录（例如，在 `host` 应用中指向 `apps/host/src`）。

- **`@api`**: 存放所有与后端交互的 API 请求函数。

- **`@/model`**: 存放应用级别的状态管理模型（基于 `@edenx/runtime/model`）。

- **`@/component`**: 存放可复用的UI组件。

- **`@/pages` 或 `@/routes`**: 页面级组件或路由组件。

例如：import { getAdminUserList } from '@api/experimentInterface';

import UserCard from '@/component/UserCard';