import { MsgTemplate } from '@pa/shared/dist/src/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { FetchVersionLogParams, FetchVersionLogResponse } from '@pa/shared/dist/src/bits';
import { GetLogActionInfoResponse, GetLogIssueInfoResponse } from '@pa/shared/dist/src/alog/models';
import { Card } from '@pa/shared/dist/src/lark/larkCard';

/**
 * 所有实现在Host中的跨项目接口定义在这
 */
export interface HostRpcService {
  sendNormalMsg: (msg: MsgTemplate, userIdType: UserIdType, targetId: string) => Promise<unknown>;
  fetchBitsVersionLog: (req: FetchVersionLogParams) => Promise<FetchVersionLogResponse[]>;
  fetchExperimentErrorList: (id: string) => Promise<unknown>;
  buildErrorLarkCard: (errorMessage: string) => Card;
  buildNoActionLarkCard: () => Card;
  buildAlogUserActionLarkCard: (actionResponse: GetLogActionInfoResponse, issueInfo: GetLogIssueInfoResponse) => Card;
  getMeegoChatId: (meego: string, meegoId: number, pmEmail: string | undefined) => Promise<{ chatId: string }>;
  isAfterThirdGray: (version: string, appId: number) => Promise<boolean>;
  isFullRelease: (version: string, appId: number) => Promise<boolean>;
  getLibraControlSetting: () => Promise<boolean>;
  getExperimentWhiteList: () => Promise<unknown>;
  addExperimentInfo: (data: unknown) => Promise<unknown>;
  addExperimentLTInfo: (data: unknown) => Promise<unknown>;
  addExperimentGroupInfo: (data: unknown) => Promise<unknown>;
  addExperimentFeedbackInfo: (data: unknown) => Promise<unknown>;
  addExperimentOtherInfo: (data: unknown, type: string) => Promise<unknown>;
  getLast2Version: (appId: number) => Promise<string[]>;
  slardarAlarmCluster: (body: never) => Promise<boolean>;
  getRetouchExperimentWarningVersion: (appId: number) => Promise<{ version: string }>;
  addExperimentCommercialInfo: (data: unknown) => Promise<unknown>;
  getVersionIntegrationTime: (version: string, appId: number) => Promise<number | undefined>;
  getVersionIntegrationEndTime: (version: string, appId: number) => Promise<number | undefined>;
  getVersionList: (appId: number) => Promise<unknown[]>;
  getPullOfflineVersion: (appId: number) => Promise<string[]>;
  getPullVersionsByIntegrationTime: (appId: number) => Promise<string[]>;
  getAnalysis: (url: string) => Promise<string>;
}

export const HostRpcServiceSymbol = Symbol.for('HostRpcService');
