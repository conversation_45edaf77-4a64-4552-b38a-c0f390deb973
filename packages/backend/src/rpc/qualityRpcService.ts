import { CardCallback } from '@pa/shared/dist/src/lark/larkCard';
import {
  GetLogActionInfoRequest,
  GetLogActionInfoResponse,
  GetLogIssueInfoRequest,
  GetLogIssueInfoResponse,
} from '@pa/shared/dist/src/alog/models';
import { CustomAssignArgs } from '@pa/shared/dist/src/assign/customAssignArgs';
import {
  LibraHitResult,
  RequestLibraPatrolConfigDaoType,
  LibraVersionPlatform,
  SendToHostFlightInfo,
} from '@pa/shared/dist/src/libra/LibraAttributionModel';
import { BlameModel } from '@pa/shared/dist/src/assign/blameModel';

/**
 * 所有实现在Quality中的跨项目接口定义在这
 */
export interface QualityRpcService {
  dispatchQualityCardAction: (data: CardCallback) => Promise<unknown>;
  queryJwtToken: (accessKey: string) => Promise<unknown>;
  addReviewVersion: (data: unknown) => Promise<unknown>;
  clearReviewVersion: (data: unknown) => Promise<unknown>;
  getAlogActionInfos: (data: GetLogActionInfoRequest) => Promise<GetLogActionInfoResponse>;
  getAlogIssueInfo: (data: GetLogIssueInfoRequest) => Promise<GetLogIssueInfoResponse>;
  testMetricsAlarm: () => Promise<void>;
  notifyMetricsAlarm: (data: unknown) => Promise<unknown>;
  fetchSlardarValues: (versionCode: number, timeStamp: number) => Promise<unknown>;
  isMainArchFocusExp: (flightId: string) => Promise<{ hit: boolean; reason: string }>;
  libraLaunchGrayNotify: (
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) => Promise<unknown>; // 根据 APP 版本通知开启灰度实验
  libraCloseGrayNotify: (
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) => Promise<unknown>; // 根据 APP 版本通知关闭灰度实验
  libraLaunchReleaseNotify: (
    appId: number,
    releaseVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) => Promise<unknown>; // 根据 APP 版本通知开启正式实验
  forbidLibraLaunchGrayNotify: (libraInfo: unknown) => Promise<unknown>; // 不再发送开启灰度实验提醒
  forbidLibra100PercentGrayNotify: (libraInfo: unknown) => Promise<unknown>; // 不再发送100%灰度实验提醒
  forbidLibraCloseGrayNotify: (libraInfo: unknown) => Promise<unknown>; // 不再发送关闭灰度实验提醒
  closeLibra: (info: unknown, messageId: string) => Promise<unknown>; // 关闭实验
  forbidLibraLaunchReleaseNotify: (libraInfo: unknown) => Promise<unknown>; // 不再发送开启正式实验提醒
  forwardCNLibraEvent: (data: unknown, eventbusId: string) => Promise<void>; // 转发 CN 事件
  overdueLibraNotify: (appId: number, version: string, meegoVersion: string, fullReleaseTime: number) => Promise<void>; // 逾期实验提醒
  // 检测是否有灰度实验且流量开启到100%
  checkGrayLibraOk: (
    storyId: number,
  ) => Promise<{ result: boolean; reason: string; flight_id: number; flight_name: string }>;
  getLibraControlRecord: (query: unknown) => Promise<unknown>; // 查询 Libra 管控记录
  libraAttribution: (dids: string, appid: number, isOversea: boolean) => Promise<LibraHitResult[]>;
  getMeegoTree: (fieldKey: string) => Promise<unknown>;
  getMetricAlarmRecord: (appId: number, version: string) => Promise<unknown>;
  requestLibraPatrolConfigDao: (data: unknown, type: RequestLibraPatrolConfigDaoType) => Promise<unknown>;
  getBusinessInfoById: (appId: number) => Promise<string[]>;
  getUserMemberList: () => Promise<unknown>;
  checkHasGrayLibra: (meegoId: string) => Promise<boolean>; // 封版时检测绑定的meego单是否创建并开启了实验
  // 普通需求，非免测需求
  checkGrayLibra100Percent: (
    storyId: number,
  ) => Promise<{ result: boolean; reason: string; flightInfo: SendToHostFlightInfo[] }>;
  libraGray100PercentNotify: (
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
    meegoIds: number[],
  ) => Promise<unknown>; // 根据 APP 版本通知开启灰度实验100%流量
  storeLibraGray100PercentExemptForm: (
    appId: number[],
    flightId: number,
    flightName: string,
    influence: string,
    exemptReason: string,
    improveMeasure: string,
    meegoId: number,
    meegoName: string,
    ownerEmail: string,
    version: string[],
  ) => Promise<unknown>;
  saveAbnormalFLightInfo: (
    meegoId: number,
    flightId: number,
    type: number,
    version?: string,
    appId?: number,
  ) => Promise<unknown>;
  getBlameModelByStack: (args: CustomAssignArgs, nativeStack: boolean) => Promise<BlameModel>;
  getLibraNewInfoList: (query: unknown) => Promise<unknown>;
  libraNewInfoFindOne: (query: unknown) => Promise<unknown>;
  createLibraNewInfo: (region: number, app: number, flightId: number) => Promise<unknown>;
  getLibraPatrolConfig: (appId: number, type: string, query?: unknown) => Promise<unknown>;
  getLibraVersionPlatform: (flightId: number) => Promise<LibraVersionPlatform>;
  updateCircuitAlarm: (aid: number, os: string, ruleIdList: number[], versionCode: string) => Promise<unknown>;
  getFlightInfoWithTime: (
    libraId: number,
    version: string,
    minTimestamp: number,
    maxTimestamp: number,
  ) => Promise<unknown>;
  getExperimentsByReleaseVersion: (releaseVersion: string, appId?: number) => Promise<unknown[]>;
  getExperimentsByMeegoInfo: (meegoId?: number, meegoName?: string) => Promise<unknown[]>;
  getLatestCountByTimestamp: (aid: number, version_code: string, timestamp: number) => Promise<number>;
}

export const QualityRpcServiceSymbol = Symbol.for('QualityRpcService');
