import { TagColor } from '@douyinfe/semi-ui/lib/es/tag';
import {
  LibraInfoColumnDefaultSetting,
  LibraInfoColumnFilterConfigs,
  LibraNewInfoTableColumnConfig,
  LibraNewInfoTableColumnGroup,
  LibraNewInfoTablePageStatus,
} from '@shared/libra/LibraNewInfoTableColumnSetting';
import {
  LibraFlightCloseFullReleaseSubType,
  LibraFlightCloseMainType,
  LibraFlightCloseOfflineSubType,
  LibraFlightCloseReopenSubType,
  LibraFlightCloseReopenSubTypeCanPreInterceptType,
  LibraFlightCloseReopenSubTypeDetailType,
  LibraFlightHasNoCloseAttribution,
  LibraFlightStatus,
  LibraNewInfo,
  LibraReopenReasonType,
  LibraTimeQueryType,
} from '@shared/libra/LibraNewInfo';
import {
  ConditionType,
  FilterGroupType,
  FilterOptionType,
  FilterRuleDesc,
  generateFilterRuleQuery,
  TreeEnumOptions,
} from '@shared/utils/conditionFilter';
import { LibraRegion } from '@shared/libra/commonLibra';
import { LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import { Flight } from '@shared/libra/flight';
import { FlightConclusionClassConfig } from '@shared/libra/FlightMetricConclusion';
import { ALL_CORE_METRICS_CONFIG } from '@shared/libra/LibraMetricConfig';
import { StoryRevenueLibraMetaInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueLibraMetaInfo';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { LibraBaseInfo } from '@shared/libra/libraInfo';
import { User } from '@pa/shared/dist/src/core';
import { AppId2Name } from '@shared/libra/common';
export const LibraBusinessSelectOptions = [
  {
    value: '1775',
    label: '剪映-App',
  },
  {
    value: '3006',
    label: 'CapCut-App',
  },
  {
    value: '3704',
    label: '剪映-PC',
  },
  {
    value: '359289',
    label: 'CapCut-PC',
  },
  {
    value: '548669',
    label: '剪映-Web',
  },
  {
    value: '348188',
    label: 'CapCut-Web',
  },
  {
    value: '2515',
    label: '醒图-App',
  },
  {
    value: '7356',
    label: 'Hypic-App',
  },
];

export enum LIBRA_APP_ID {
  JianYingApp = 147,
  JianYingPC = 399,
  RetouchApp = 255,
  CapCutApp = 305,
  CapCutApp_VA = 295, // VA 美东 libra 空间
  HypicApp = 367,
  HypicApp_VA = 371, // VA 美东 libra 空间
  CapCutPC = 360,
  CapCutPC_VA = 368, // VA 美东 libra 空间
  TikTokApp = 22,
  DouYinApp = 12,
  DouYinFastApp = 142,
  CapCutWeb = 381,
  JianYingWeb = 1071,
  JiMengApp = 992,
  JiMengWeb = 1021,
  DreaminaWeb = 480,
  CapcutBusiness = 474, // capcut_business(Capcut电商版)
}

export enum APP_NAME {
  JianYingApp = '剪映',
  JianYingPC = '剪映专业版',
  RetouchApp = '醒图',
  CapCutApp = 'CapCut',
  HypicApp = 'Hypic',
  CapCutPC = 'CapCut专业版',
  TikTokApp = 'TikTok',
  DouYinApp = '抖音',
  DouYinFastApp = '抖音极速版',
  CapCutWeb = 'CapCut_Web',
  JianYingWeb = 'jianying_web',
  JiMengApp = 'dreamina_app',
  JiMengWeb = 'xweb',
  DreaminaWeb = 'mweb',
  CapcutBusiness = 'capcut_business', // capcut_business(Capcut电商版)
}

export enum PRODUCT_APP_ID {
  JianYingApp = '1775',
  JianYingPC = '3704',
  RetouchApp = '2515',
  CapCutApp = '3006',
  HypicApp = '7356',
  CapCutPC = '359289',
  TikTokApp = '1180',
  DouYinApp = '1128',
  DouYinFastApp = '2329',
  CapCutWeb = '348188',
  JianYingWeb = '548669',
  JiMengApp = '581595',
  JiMengWeb = '513695',
  DreaminaWeb = '513641',
  CapcutBusiness = '573081', // capcut_business(Capcut电商版)
}

// Meego 上显示的上线应用
export enum MEEGO_PRODUCT_NAME {
  JianYingApp = 'App-剪映',
  JianYingPC = 'PC-剪映专业版',
  RetouchApp = '醒图',
  CapCutApp = 'App-CapCut',
  HypicApp = 'Hypic',
  CapCutPC = 'PC-CapCut专业版',
  TikTokApp = 'TikTok',
  DouYinApp = 'App-抖音',
  DouYinFastApp = 'App-抖音极速版',
  CapCutWeb = 'Web-CapCut',
  JianYingWeb = 'Web-剪映',
  JiMengApp = 'APP-即梦',
  JiMengWeb = 'Web-即梦',
  DreaminaWeb = 'Web-Dreamina',
  CapcutBusiness = 'Web-营销工具(Pippit)', // capcut_business(Capcut电商版)
}

export const LIBRA_APP_CONFIGS: {
  libraAppId: number;
  productAppId: string;
  appName: string;
  meegoAppName: string;
}[] = [
  {
    libraAppId: LIBRA_APP_ID.JianYingApp,
    productAppId: PRODUCT_APP_ID.JianYingApp,
    appName: APP_NAME.JianYingApp,
    meegoAppName: MEEGO_PRODUCT_NAME.JianYingApp,
  },
  {
    libraAppId: LIBRA_APP_ID.JianYingPC,
    productAppId: PRODUCT_APP_ID.JianYingPC,
    appName: APP_NAME.JianYingPC,
    meegoAppName: MEEGO_PRODUCT_NAME.JianYingPC,
  },
  {
    libraAppId: LIBRA_APP_ID.RetouchApp,
    productAppId: PRODUCT_APP_ID.RetouchApp,
    appName: APP_NAME.RetouchApp,
    meegoAppName: MEEGO_PRODUCT_NAME.RetouchApp,
  },
  {
    libraAppId: LIBRA_APP_ID.CapCutApp,
    productAppId: PRODUCT_APP_ID.CapCutApp,
    appName: APP_NAME.CapCutApp,
    meegoAppName: MEEGO_PRODUCT_NAME.CapCutApp,
  },
  {
    libraAppId: LIBRA_APP_ID.CapCutApp_VA,
    productAppId: PRODUCT_APP_ID.CapCutApp,
    appName: APP_NAME.CapCutApp,
    meegoAppName: MEEGO_PRODUCT_NAME.CapCutApp,
  },
  {
    libraAppId: LIBRA_APP_ID.HypicApp,
    productAppId: PRODUCT_APP_ID.HypicApp,
    appName: APP_NAME.HypicApp,
    meegoAppName: MEEGO_PRODUCT_NAME.HypicApp,
  },
  {
    libraAppId: LIBRA_APP_ID.HypicApp_VA,
    productAppId: PRODUCT_APP_ID.HypicApp,
    appName: APP_NAME.HypicApp,
    meegoAppName: MEEGO_PRODUCT_NAME.HypicApp,
  },
  {
    libraAppId: LIBRA_APP_ID.CapCutPC,
    productAppId: PRODUCT_APP_ID.CapCutPC,
    appName: APP_NAME.CapCutPC,
    meegoAppName: MEEGO_PRODUCT_NAME.CapCutPC,
  },
  {
    libraAppId: LIBRA_APP_ID.CapCutPC_VA,
    productAppId: PRODUCT_APP_ID.CapCutPC,
    appName: APP_NAME.CapCutPC,
    meegoAppName: MEEGO_PRODUCT_NAME.CapCutPC,
  },
  {
    libraAppId: LIBRA_APP_ID.TikTokApp,
    productAppId: PRODUCT_APP_ID.TikTokApp,
    appName: APP_NAME.TikTokApp,
    meegoAppName: MEEGO_PRODUCT_NAME.TikTokApp,
  },
  {
    libraAppId: LIBRA_APP_ID.DouYinApp,
    productAppId: PRODUCT_APP_ID.DouYinApp,
    appName: APP_NAME.DouYinApp,
    meegoAppName: MEEGO_PRODUCT_NAME.DouYinApp,
  },
  {
    libraAppId: LIBRA_APP_ID.DouYinFastApp,
    productAppId: PRODUCT_APP_ID.DouYinFastApp,
    appName: APP_NAME.DouYinFastApp,
    meegoAppName: MEEGO_PRODUCT_NAME.DouYinFastApp,
  },
  {
    libraAppId: LIBRA_APP_ID.CapCutWeb,
    productAppId: PRODUCT_APP_ID.CapCutWeb,
    appName: APP_NAME.CapCutWeb,
    meegoAppName: MEEGO_PRODUCT_NAME.CapCutWeb,
  },
  {
    libraAppId: LIBRA_APP_ID.JianYingWeb,
    productAppId: PRODUCT_APP_ID.JianYingWeb,
    appName: APP_NAME.JianYingWeb,
    meegoAppName: MEEGO_PRODUCT_NAME.JianYingWeb,
  },
  {
    libraAppId: LIBRA_APP_ID.JiMengApp,
    productAppId: PRODUCT_APP_ID.JiMengApp,
    appName: APP_NAME.JiMengApp,
    meegoAppName: MEEGO_PRODUCT_NAME.JiMengApp,
  },
  {
    libraAppId: LIBRA_APP_ID.JiMengWeb,
    productAppId: PRODUCT_APP_ID.JiMengWeb,
    appName: APP_NAME.JiMengWeb,
    meegoAppName: MEEGO_PRODUCT_NAME.JiMengWeb,
  },
  {
    libraAppId: LIBRA_APP_ID.DreaminaWeb,
    productAppId: PRODUCT_APP_ID.DreaminaWeb,
    appName: APP_NAME.DreaminaWeb,
    meegoAppName: MEEGO_PRODUCT_NAME.DreaminaWeb,
  },
  {
    libraAppId: LIBRA_APP_ID.CapcutBusiness,
    productAppId: PRODUCT_APP_ID.CapcutBusiness,
    appName: APP_NAME.CapcutBusiness,
    meegoAppName: MEEGO_PRODUCT_NAME.CapcutBusiness,
  },
];

export const getLibraAppIdByAppId = (appId: string) => {
  const config = LIBRA_APP_CONFIGS.find(item => item.productAppId === appId);
  return config?.libraAppId || 0;
};

export const getAppIdByLibraAppId = (libraAppId: number) => {
  const config = LIBRA_APP_CONFIGS.find(item => item.libraAppId === libraAppId);
  return config?.productAppId || '';
};

export const getAppNameByLibraAppId = (libraAppId: number) => {
  const config = LIBRA_APP_CONFIGS.find(item => item.libraAppId === libraAppId);
  return config?.appName || '';
};

// 获取 Meego 显示的 App 名称（通过 Libra App Id）
export const getMeegoAppNameByLibraAppId = (libraAppId: number) => {
  const config = LIBRA_APP_CONFIGS.find(item => item.libraAppId === libraAppId);
  return config?.meegoAppName || '';
};

// 获取 Meego 显示的 App 名称（通过产品 App Id）
export const getMeegoAppNameByAppId = (appId: string) => {
  const config = LIBRA_APP_CONFIGS.find(item => item.productAppId === appId);
  return config?.meegoAppName || '';
};

// 根据“实验Id + 区域”获取 Libra 链接
export const libraDetailUrl = (region: LibraRegion, flightId: number) => {
  if (region === LibraRegion.CN) {
    return `https://data.bytedance.net/libra/flight/${flightId}/edit`;
  }
  if (region === LibraRegion.SG) {
    return `https://libra-sg.tiktok-row.net/libra/flight/${flightId}/edit`;
  }
  if (region === LibraRegion.VA) {
    return `https://libra-va.tiktok-row.net/libra/flight/${flightId}/edit`;
  }
  return '';
};

// 根据 Libra 链接获取所属区域
export const libraRegionByUrl = (url: string) => {
  if (url.includes('data.bytedance.net')) {
    return LibraRegion.CN;
  }
  if (url.includes('libra-sg.tiktok-row.net')) {
    return LibraRegion.SG;
  }
  if (url.includes('libra-va.tiktok-row.net')) {
    return LibraRegion.VA;
  }
  return LibraRegion.UNKNOWN;
};

// 根据 LibraAppId 和 FlightId 生成纸飞机实验链接
export const libraUrlOfPaperAirplane = (flightId: number, libraAppId: number) => {
  const appid = getAppIdByLibraAppId(libraAppId);
  return `https://pa.bytedance.net/libra/list?libra_business_id=${appid}&libra_flight_id=${flightId}`;
};

// 时间格式：'2024-07-19 14:47:16'
export const libraConvertTimeStrToTimestamp = (timeStr: string) => {
  const date = new Date(timeStr);
  return Math.floor(date.getTime() / 1000);
};

// unix 时间戳(秒)转换为日期格式：'2024-12-16'
export const libraConvertTimestampToDateStr = (timestamp: number, joinStr = '-') =>
  new Date(timestamp * 1000)
    .toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      timeZone: 'Asia/Shanghai',
    })
    .split('/')
    .join(joinStr);

// 将浮点数转成百分比
export const numberToPercentageStr = (value: number, decimals = 3) => {
  if (isNaN(value)) {
    return '0%';
  }
  return `${(value * 100).toFixed(decimals)}%`;
};

// 保留固定小数位，并转换为字符串
export const numberToFixedNumberStr = (value: number, decimals = 6) => {
  if (isNaN(value)) {
    return '0';
  }
  return `${value.toFixed(decimals)}`;
};

// 获取实验报告生成的起止时间(通过 Libra API 查询的方式生成报告)
// t_n：表示是 T-n，默认是 T-2 时间
export const libraReportDateRangeByAPI = (libraStartTimeStr: string, libraEndTimeStr: string, t_n = 2) => {
  const curTime = Math.floor(Date.now() / 1000) - t_n * 86400;
  const flightStartTime = libraConvertTimeStrToTimestamp(libraStartTimeStr);
  const flightEndTime = libraConvertTimeStrToTimestamp(libraEndTimeStr);
  // #### Libra API 的方式
  const reportStartTime = flightStartTime;
  let reportEndTime = flightEndTime;
  if (reportEndTime > curTime) {
    // (today - 2) 时间
    reportEndTime = curTime;
  } else {
    // (end_time - 1) 时间
    reportEndTime = reportEndTime - 86400;
    if (reportEndTime < reportStartTime) {
      // 如果 reportEndTime 小于 reportStartTime，则赋值为 reportStartTime
      reportEndTime = reportStartTime;
    }
  }
  return {
    reportStartDate: libraConvertTimestampToDateStr(reportStartTime),
    reportEndDate: libraConvertTimestampToDateStr(reportEndTime),
  };
};

// 获取实验报告生成的起止时间(通过 Hive 表查询的方式生成报告)
export const libraReportDateRangeByHive = (libraStartTimeStr: string, libraEndTimeStr: string) =>
  // 实验结束时间如果大于今天(now)，则取 t-4。原因：受限于 Hive 表目前仅能提供 t-4 的可靠数据
  libraReportDateRangeByAPI(libraStartTimeStr, libraEndTimeStr, 4);

// 生成实验报告链接
export const libraReportUrlByDateStr = (
  flightId: number,
  region: LibraRegion,
  reportStartDate: string,
  reportEndDate: string,
) => {
  const suffix = `conclusion-report?end_date=${reportEndDate}&start_date=${reportStartDate}`;
  if (region === LibraRegion.CN) {
    return `https://data.bytedance.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === LibraRegion.SG) {
    return `https://libra-sg.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === LibraRegion.VA) {
    return `https://libra-va.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  return '';
};

// 生成实验报告链接
export const libraReportUrl = (flight: Flight, region: LibraRegion) => {
  const flightId = flight.id;
  const { reportStartDate, reportEndDate } = libraReportDateRangeByAPI(flight.start_time, flight.end_time);
  return libraReportUrlByDateStr(flightId, region, reportStartDate, reportEndDate);
};

// 生成实验报告链接(方式2)
export const libraReportUrl2 = (libraMetaInfo: StoryRevenueLibraMetaInfo) => {
  const { flightId, region, startTime } = libraMetaInfo;
  let { endTime } = libraMetaInfo;
  const nowTime = Math.floor(Date.now() / 1000) - 2 * 86400;
  if (endTime > nowTime) {
    endTime = nowTime;
  } else {
    // 根据 ds 要求，endTime 统一减去 1d
    endTime = endTime - 86400;
    if (endTime < startTime) {
      endTime = startTime;
    }
  }
  const reportEndDate = libraConvertTimestampToDateStr(endTime);
  const reportStartDate = libraConvertTimestampToDateStr(startTime);
  const suffix = `conclusion-report?end_date=${reportEndDate}&start_date=${reportStartDate}`;
  if (region === 'cn') {
    return `https://data.bytedance.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === 'sg') {
    return `https://libra-sg.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === 'va') {
    return `https://libra-va.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  return '';
};

// 生成实验报告链接(方式3)
export const libraReportUrl3 = (flightId: number, region: LibraRegion, startTime: number, endTime: number) => {
  let fixedEndTime = endTime;
  const nowTime = Math.floor(Date.now() / 1000) - 2 * 86400;
  if (fixedEndTime > nowTime) {
    fixedEndTime = nowTime;
  } else {
    // 根据 ds 要求，endTime 统一减去 1d
    fixedEndTime = fixedEndTime - 86400;
    if (fixedEndTime < startTime) {
      fixedEndTime = startTime;
    }
  }
  const reportEndDate = libraConvertTimestampToDateStr(fixedEndTime);
  const reportStartDate = libraConvertTimestampToDateStr(startTime);
  const suffix = `conclusion-report?end_date=${reportEndDate}&start_date=${reportStartDate}`;
  if (region === LibraRegion.CN) {
    return `https://data.bytedance.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === LibraRegion.SG) {
    return `https://libra-sg.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  if (region === LibraRegion.VA) {
    return `https://libra-va.tiktok-row.net/libra/flight/${flightId}/${suffix}`;
  }
  return '';
};

// 生成实验报告所需的“核心性能”指标组配置（暂时只有剪映和 CapCut）
export const libraReportMetricGroupPerformanceClassConfig = (
  flight: Flight,
  region: LibraRegion,
): FlightConclusionClassConfig | undefined => {
  const flightAppId = getAppIdByLibraAppId(flight.app_id);
  if (flightAppId === '1775') {
    // 剪映 App
    return {
      classId: 'app_core_performance',
      className: '核心性能指标',
      dim: 105427,
      // iOS: 25095686
      // Android: 24941908
      dim_vals: ['24941908', '25095686'],
      groups: [
        {
          groupId: '142079_ios',
          groupName: 'iOS',
          metrics: [
            {
              metricId: '1757495',
              metricName: '崩溃用户率',
            },
            {
              metricId: '1757496',
              metricName: '人均崩溃次数',
            },
            {
              metricId: '1757497',
              metricName: '卡死用户率',
            },
            {
              metricId: '1757498',
              metricName: '人均卡死次数',
            },
            {
              metricId: '1757499',
              metricName: 'oom用户率',
            },
            {
              metricId: '1757500',
              metricName: '人均oom次数',
            },
            {
              metricId: '1757501',
              metricName: '启动耗时',
            },
            {
              metricId: '1757502',
              metricName: '磁盘占用均值',
            },
            {
              metricId: '1757503',
              metricName: '低磁盘占比',
            },
            {
              metricId: '1757504',
              metricName: '工具端帧率',
            },
            {
              metricId: '1757505',
              metricName: '工具端低帧率占比',
            },
            {
              metricId: '1757506',
              metricName: '剪同款预览帧率',
            },
            {
              metricId: '1757507',
              metricName: '剪同款低帧率占比',
            },
            {
              metricId: '1757508',
              metricName: '工具端导出时长率',
            },
            {
              metricId: '1757509',
              metricName: '剪同款导出时长率',
            },
            {
              metricId: '1757510',
              metricName: '工具端导出成功率',
            },
            {
              metricId: '1757511',
              metricName: '剪同款导出成功率',
            },
            {
              metricId: '1757512',
              metricName: '定帧刷新耗时',
            },
            {
              metricId: '1757513',
              metricName: 'Seek耗时',
            },
            {
              metricId: '1757514',
              metricName: '素材导入耗时',
            },
            {
              metricId: '1757515',
              metricName: '草稿加载耗时',
            },
            {
              metricId: '1757516',
              metricName: '素材导入失败率',
            },
            {
              metricId: '1757517',
              metricName: '剪同款导入成功率',
            },
            {
              metricId: '1757518',
              metricName: '剪同款导入耗时',
            },
            {
              metricId: '1757519',
              metricName: '模板发布成功率',
            },
            {
              metricId: '1757520',
              metricName: '模板发布耗时',
            },
            {
              metricId: '1757521',
              metricName: '二创发布成功率',
            },
            {
              metricId: '1757522',
              metricName: '锚点成功率',
            },
            {
              metricId: '1757523',
              metricName: '锚点耗时',
            },
            {
              metricId: '1757524',
              metricName: '内流首帧耗时',
            },
            {
              metricId: '1757525',
              metricName: '播放失败率',
            },
          ],
        },
        {
          groupId: '142079_android',
          groupName: 'Android',
          metrics: [
            {
              metricId: '1757495',
              metricName: '崩溃用户率',
            },
            {
              metricId: '1757496',
              metricName: '人均崩溃次数',
            },
            {
              metricId: '1757497',
              metricName: '卡死用户率',
            },
            {
              metricId: '1757498',
              metricName: '人均卡死次数',
            },
            {
              metricId: '1757499',
              metricName: 'oom用户率',
            },
            {
              metricId: '1757500',
              metricName: '人均oom次数',
            },
            {
              metricId: '1757501',
              metricName: '启动耗时',
            },
            {
              metricId: '1757502',
              metricName: '磁盘占用均值',
            },
            {
              metricId: '1757503',
              metricName: '低磁盘占比',
            },
            {
              metricId: '1757504',
              metricName: '工具端帧率',
            },
            {
              metricId: '1757505',
              metricName: '工具端低帧率占比',
            },
            {
              metricId: '1757506',
              metricName: '剪同款预览帧率',
            },
            {
              metricId: '1757507',
              metricName: '剪同款低帧率占比',
            },
            {
              metricId: '1757508',
              metricName: '工具端导出时长率',
            },
            {
              metricId: '1757509',
              metricName: '剪同款导出时长率',
            },
            {
              metricId: '1757510',
              metricName: '工具端导出成功率',
            },
            {
              metricId: '1757511',
              metricName: '剪同款导出成功率',
            },
            {
              metricId: '1757512',
              metricName: '定帧刷新耗时',
            },
            {
              metricId: '1757513',
              metricName: 'Seek耗时',
            },
            {
              metricId: '1757514',
              metricName: '素材导入耗时',
            },
            {
              metricId: '1757515',
              metricName: '草稿加载耗时',
            },
            {
              metricId: '1757516',
              metricName: '素材导入失败率',
            },
            {
              metricId: '1757517',
              metricName: '剪同款导入成功率',
            },
            {
              metricId: '1757518',
              metricName: '剪同款导入耗时',
            },
            {
              metricId: '1757519',
              metricName: '模板发布成功率',
            },
            {
              metricId: '1757520',
              metricName: '模板发布耗时',
            },
            {
              metricId: '1757521',
              metricName: '二创发布成功率',
            },
            {
              metricId: '1757522',
              metricName: '锚点成功率',
            },
            {
              metricId: '1757523',
              metricName: '锚点耗时',
            },
            {
              metricId: '1757524',
              metricName: '内流首帧耗时',
            },
            {
              metricId: '1757525',
              metricName: '播放失败率',
            },
          ],
        },
      ],
    } as FlightConclusionClassConfig;
  }

  if (flightAppId === '3006' && region === LibraRegion.SG) {
    // CapCut App
    return {
      classId: 'app_core_performance',
      className: '核心性能指标',
      dim: 58761,
      // iOS: 25095686
      // Android: 24941908
      dim_vals: ['131819675', '131819674'],
      groups: [
        {
          groupId: '7042455_ios',
          groupName: 'iOS',
          metrics: [
            {
              metricId: '904376',
              metricName: '崩溃用户率',
            },
            {
              metricId: '904377',
              metricName: '人均崩溃次数',
            },
            {
              metricId: '904378',
              metricName: '卡死用户率',
            },
            {
              metricId: '904379',
              metricName: '人均卡死次数',
            },
            {
              metricId: '904380',
              metricName: 'oom用户率',
            },
            {
              metricId: '904381',
              metricName: '人均oom次数',
            },
            {
              metricId: '904382',
              metricName: '启动耗时',
            },
            {
              metricId: '904383',
              metricName: '磁盘占用均值',
            },
            {
              metricId: '904384',
              metricName: '磁盘低于500M用户的比例',
            },
            {
              metricId: '904385',
              metricName: '工具端预览帧率',
            },
            {
              metricId: '904386',
              metricName: '工具端帧率低于20帧的占比',
            },
            {
              metricId: '904387',
              metricName: '剪同款预览帧率',
            },
            {
              metricId: '904388',
              metricName: '剪同款帧率低于20帧的占比',
            },
            {
              metricId: '904389',
              metricName: '工具端导出时长率',
            },
            {
              metricId: '904390',
              metricName: '剪同款导出时长率',
            },
            {
              metricId: '904391',
              metricName: '工具端导出成功率',
            },
            {
              metricId: '904392',
              metricName: '剪同款导出成功率',
            },
            {
              metricId: '904393',
              metricName: '定帧刷新耗时',
            },
            {
              metricId: '904394',
              metricName: 'Seek耗时',
            },
            {
              metricId: '904395',
              metricName: '素材导入耗时',
            },
            {
              metricId: '904396',
              metricName: '草稿加载耗时',
            },
            {
              metricId: '904397',
              metricName: '素材导入失败率',
            },
            {
              metricId: '904398',
              metricName: '剪同款导入成功率',
            },
            {
              metricId: '904399',
              metricName: '剪同款导入耗时',
            },
            {
              metricId: '904400',
              metricName: '模板发布成功率',
            },
            {
              metricId: '904401',
              metricName: '模板发布耗时',
            },
            {
              metricId: '904402',
              metricName: '二创发布成功率',
            },
            {
              metricId: '904403',
              metricName: '锚点冷起成功率',
            },
            {
              metricId: '904404',
              metricName: '锚点冷启耗时',
            },
            {
              metricId: '904405',
              metricName: '内流首帧耗时',
            },
            {
              metricId: '904406',
              metricName: '播放失败率',
            },
          ],
        },
        {
          groupId: '7042455_android',
          groupName: 'Android',
          metrics: [
            {
              metricId: '904376',
              metricName: '崩溃用户率',
            },
            {
              metricId: '904377',
              metricName: '人均崩溃次数',
            },
            {
              metricId: '904378',
              metricName: '卡死用户率',
            },
            {
              metricId: '904379',
              metricName: '人均卡死次数',
            },
            {
              metricId: '904380',
              metricName: 'oom用户率',
            },
            {
              metricId: '904381',
              metricName: '人均oom次数',
            },
            {
              metricId: '904382',
              metricName: '启动耗时',
            },
            {
              metricId: '904383',
              metricName: '磁盘占用均值',
            },
            {
              metricId: '904384',
              metricName: '磁盘低于500M用户的比例',
            },
            {
              metricId: '904385',
              metricName: '工具端预览帧率',
            },
            {
              metricId: '904386',
              metricName: '工具端帧率低于20帧的占比',
            },
            {
              metricId: '904387',
              metricName: '剪同款预览帧率',
            },
            {
              metricId: '904388',
              metricName: '剪同款帧率低于20帧的占比',
            },
            {
              metricId: '904389',
              metricName: '工具端导出时长率',
            },
            {
              metricId: '904390',
              metricName: '剪同款导出时长率',
            },
            {
              metricId: '904391',
              metricName: '工具端导出成功率',
            },
            {
              metricId: '904392',
              metricName: '剪同款导出成功率',
            },
            {
              metricId: '904393',
              metricName: '定帧刷新耗时',
            },
            {
              metricId: '904394',
              metricName: 'Seek耗时',
            },
            {
              metricId: '904395',
              metricName: '素材导入耗时',
            },
            {
              metricId: '904396',
              metricName: '草稿加载耗时',
            },
            {
              metricId: '904397',
              metricName: '素材导入失败率',
            },
            {
              metricId: '904398',
              metricName: '剪同款导入成功率',
            },
            {
              metricId: '904399',
              metricName: '剪同款导入耗时',
            },
            {
              metricId: '904400',
              metricName: '模板发布成功率',
            },
            {
              metricId: '904401',
              metricName: '模板发布耗时',
            },
            {
              metricId: '904402',
              metricName: '二创发布成功率',
            },
            {
              metricId: '904403',
              metricName: '锚点冷起成功率',
            },
            {
              metricId: '904404',
              metricName: '锚点冷启耗时',
            },
            {
              metricId: '904405',
              metricName: '内流首帧耗时',
            },
            {
              metricId: '904406',
              metricName: '播放失败率',
            },
          ],
        },
      ],
    } as FlightConclusionClassConfig;
  }
};

// 生成实验报告所需的“核心体验”指标组配置（暂时只有剪映和 CapCut）
export const libraReportMetricGroupExperienceClassConfig = (
  flight: Flight,
  region: LibraRegion,
): FlightConclusionClassConfig | undefined => {
  const flightAppId = getAppIdByLibraAppId(flight.app_id);
  if (flightAppId === '1775') {
    // 剪映 App
    return {
      classId: 'app_core_experience',
      className: '核心体验指标',
      groups: [
        {
          groupId: '111174',
          groupName: 'iOS',
          metrics: [
            {
              metricId: '1385839',
              metricName: '编辑页进首帧（pct50）',
            },
            {
              metricId: '1385840',
              metricName: '剪同款页进首帧（pct50）',
            },
            {
              metricId: '1373900',
              metricName: '编辑页进首帧（pct90）',
            },
            {
              metricId: '1373901',
              metricName: '剪同款页进首帧（pct90）',
            },
            {
              metricId: '1385841',
              metricName: '编辑页进首屏（pct50）',
            },
            {
              metricId: '1385842',
              metricName: '剪同款页进首屏（pct50）',
            },
            {
              metricId: '1373898',
              metricName: '编辑页进首屏（pct90）',
            },
            {
              metricId: '1373899',
              metricName: '剪同款页进首屏（pct90）',
            },
            {
              metricId: '1468387',
              metricName: '用户体感时间（pct50）',
            },
            {
              metricId: '1468388',
              metricName: '用户体感时间（pct90）',
            },
          ],
        },
        {
          groupId: '113816',
          groupName: 'Android',
          metrics: [
            {
              metricId: '1543452',
              metricName: '均值',
            },
            {
              metricId: '1543453',
              metricName: 'PCT50',
            },
            {
              metricId: '1543454',
              metricName: 'PCT90',
            },
            {
              metricId: '1543455',
              metricName: 'PCT50(剪辑)',
            },
            {
              metricId: '1543456',
              metricName: 'PCT90(剪辑)',
            },
            {
              metricId: '1543457',
              metricName: 'PCT50(社区)',
            },
            {
              metricId: '1543458',
              metricName: 'PCT90(社区)',
            },
          ],
        },
      ],
    } as FlightConclusionClassConfig;
  }

  if (flightAppId === '3006' && region === LibraRegion.SG) {
    // CapCut App
    return {
      classId: 'app_core_experience',
      className: '核心体验指标',
      groups: [
        {
          groupId: '7028942',
          groupName: 'iOS',
          metrics: [
            {
              metricId: '718499',
              metricName: '编辑页进首帧（pct50）',
            },
            {
              metricId: '718500',
              metricName: '剪同款页进首帧（pct50）',
            },
            {
              metricId: '718501',
              metricName: '编辑页进首帧（pct90）',
            },
            {
              metricId: '718502',
              metricName: '剪同款页进首帧（pct90）',
            },
            {
              metricId: '718503',
              metricName: '编辑页进首屏（pct50）',
            },
            {
              metricId: '718504',
              metricName: '剪同款页进首屏（pct50）',
            },
            {
              metricId: '718505',
              metricName: '编辑页进首屏（pct90）',
            },
            {
              metricId: '718506',
              metricName: '剪同款页进首屏（pct90）',
            },
            {
              metricId: '778227',
              metricName: '剪同款页体感时间（pct50）',
            },
            {
              metricId: '778228',
              metricName: '编辑页体感时间（pct50）',
            },
          ],
        },
        {
          groupId: '7028535',
          groupName: 'Android',
          metrics: [
            {
              metricId: '860003',
              metricName: '均值',
            },
            {
              metricId: '860004',
              metricName: 'PCT50',
            },
            {
              metricId: '860005',
              metricName: 'PCT90',
            },
            {
              metricId: '805243',
              metricName: 'PCT50(剪辑)',
            },
            {
              metricId: '805244',
              metricName: 'PCT90(剪辑)',
            },
            {
              metricId: '805245',
              metricName: 'PCT50(社区)',
            },
            {
              metricId: '805246',
              metricName: 'PCT90(社区)',
            },
          ],
        },
      ],
    } as FlightConclusionClassConfig;
  }
};

// 生成实验报告所需的“所有”指标组配置(App 核心业务指标 + App 核心性能指标)
export const libraReportMetricGroupAllClassesConfig = (
  flight: Flight,
  region: LibraRegion,
): { classes: FlightConclusionClassConfig[] } => {
  const classes: FlightConclusionClassConfig[] = [];
  const flightAppId = getAppIdByLibraAppId(flight.app_id);
  const groups: {
    groupId: string;
    groupName: string;
    metrics: {
      metricId: string;
      metricName: string;
    }[];
  }[] = [];
  if (ALL_CORE_METRICS_CONFIG[flightAppId] === undefined) {
    return { classes };
  }
  // APP 必看指标
  for (const metric_group_info of ALL_CORE_METRICS_CONFIG[flightAppId].metric_groups) {
    const { metric_group_id } = metric_group_info; // 指标组ID
    const { metric_group_name } = metric_group_info; // 指标组名称
    const { metric_list } = metric_group_info; // 指标列表
    const metrics: {
      metricId: string;
      metricName: string;
    }[] = [];
    for (const metric_info of metric_list) {
      metrics.push({
        metricId: String(metric_info.metric_id),
        metricName: metric_info.metric_name,
      });
    }
    groups.push({
      groupId: String(metric_group_id),
      groupName: metric_group_name,
      metrics,
    });
  }
  const appCoreMetricsClassConfig = {
    classId: 'app_core_business',
    className: 'APP必看',
    groups,
  } as FlightConclusionClassConfig;
  classes.push(appCoreMetricsClassConfig);

  // 核心性能指标（暂时只有剪映和 CapCut）
  const appCorePerformanceMetricsClassConfig = libraReportMetricGroupPerformanceClassConfig(flight, region);
  if (appCorePerformanceMetricsClassConfig) {
    classes.push(appCorePerformanceMetricsClassConfig);
  }

  // 核心体验指标（暂时只有剪映和 CapCut）
  const appCoreExperienceMetricsClassConfig = libraReportMetricGroupExperienceClassConfig(flight, region);
  if (appCoreExperienceMetricsClassConfig) {
    classes.push(appCoreExperienceMetricsClassConfig);
  }

  return { classes };
};

export const libraDetailUrlByRegionStr = (region: string, flightId: number) => {
  if (region === 'cn') {
    return libraDetailUrl(LibraRegion.CN, flightId);
  }
  if (region === 'sg') {
    return libraDetailUrl(LibraRegion.SG, flightId);
  }
  if (region === 'va') {
    return libraDetailUrl(LibraRegion.VA, flightId);
  }
  return '';
};

export const libraCreateUrl = (region: LibraRegion) => {
  if (region === LibraRegion.CN) {
    return `https://data.bytedance.net/libra/new-flight/coding`;
  }
  if (region === LibraRegion.SG) {
    return `https://libra-sg.tiktok-row.net/libra/new-flight/coding`;
  }
  if (region === LibraRegion.VA) {
    return `https://libra-va.tiktok-row.net/libra/new-flight/coding`;
  }
  return '';
};

export const libraFormatTimestamp = (timestamp: number) => {
  // Unix 时间戳通常是秒，需要转换为毫秒
  const date = new Date(timestamp * 1000);

  // 格式化日期为 YYYY-MM-DD HH:mm:ss
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export const formatTimeDifference = (startTime: number, endTime: number) => {
  // 计算时间差，以毫秒为单位
  const diffMilliseconds = endTime - startTime;

  if (diffMilliseconds < 0) {
    // throw new Error('The end date must be later than the start date');
    return '';
  }

  // 将毫秒转换为分钟、小时和天
  const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));
  const diffHours = diffMilliseconds / (1000 * 60 * 60);
  const diffDays = diffMilliseconds / (1000 * 60 * 60 * 24);

  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`;
  } else if (diffHours < 24) {
    // 去除小数点后为 .0 的情况
    const hours = diffHours.toFixed(1);
    return hours.endsWith('.0') ? `${parseInt(hours)}小时` : `${hours}小时`;
  } else {
    // 去除小数点后为 .0 的情况
    const days = diffDays.toFixed(1);
    return days.endsWith('.0') ? `${parseInt(days)}天` : `${days}天`;
  }
};

export const flightTypeDisplayName = (type: string) => {
  if (type === 'strategy') {
    return '服务端实验';
  }
  if (type === 'product') {
    return '普通客户端实验';
  }
  if (type === 'ab_client_sdk') {
    return 'AB客户端SDK实验';
  }
  if (type === 'settings_client_sdk') {
    return 'Settings SDK实验';
  }
  if (type === 'settings_client_normal') {
    return 'Settings普通客户端实验';
  }
  if (type === 'ab_local_client') {
    return '客户端本地分流实验';
  }
  if (type === 'gp_regression') {
    return '自动调参实验';
  }
  if (type === 'web_sdk') {
    return 'Web端实验';
  }
  if (type === 'ad_group') {
    return '广告组实验';
  }
  if (type === 'ad_user') {
    return '广告用户实验';
  }
  if (type === 'ad_advertiser') {
    return '广告主实验';
  }
  if (type === 'ad_plan') {
    return '广告计划实验';
  }
  if (type === 'interleaving') {
    return '搜索interleaving实验';
  }
  return type;
};

export const flightStatusColorMap: Record<number, TagColor> = {
  0: 'green',
  1: 'blue',
  2: 'yellow',
  3: 'orange',
  4: 'purple',
  91: 'green',
};

export const flightLayerTypeDisplayName = (type: number) => {
  if (type === 0) {
    return 'uid';
  }
  if (type === 1) {
    return 'did';
  }
  if (type === 2) {
    return 'rid';
  }
  if (type === 3) {
    return 'uid+did';
  }
  if (type === 4) {
    return 'uuid';
  }
  if (type === 5) {
    return 'cdid';
  }
  if (type === 6) {
    return 'ssid';
  }
  if (type === 7) {
    return 'webid';
  }
  if (type === 8) {
    return 'pkid';
  }
  if (type === 9) {
    return 'pureuid';
  }
  return '';
};

export const libraReopenType2DisplayNameMap: Record<number, string> = {
  0: '策略迭代',
  1: '产品配置变更',
  2: '实验数据不理想',
  3: '实验配置错误',
  4: '修复Bug重开',
};

export const libraStopType2DisplayNameMap: Record<number, string> = {
  1: '实验全量',
  2: '实验下线',
  3: '实验重开',
};

export const libraFlightCloseAttributionMainType2DisplayNameMap: Record<LibraFlightCloseMainType, string> = {
  [LibraFlightCloseMainType.Reopen]: '实验重开',
  [LibraFlightCloseMainType.FullRelease]: '实验全量',
  [LibraFlightCloseMainType.Offline]: '实验下线',
};

export const libraFlightCloseAttributionReopenSubType2DisplayNameMap: Record<LibraFlightCloseReopenSubType, string> = {
  [LibraFlightCloseReopenSubType.Normal]: '正常重开',
  [LibraFlightCloseReopenSubType.CodeRelatedAnomaly]: '代码相关',
  [LibraFlightCloseReopenSubType.StrategyChangeAnomaly]: '产品策略变动',
  [LibraFlightCloseReopenSubType.OtherAnomaly]: 'libra 平台问题/其他',
};

export const libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap: Record<
  LibraFlightCloseReopenSubTypeDetailType,
  string
> = {
  [LibraFlightCloseReopenSubTypeDetailType.Normal_CloseGray]: '灰度实验关闭',
  [LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits]: '正式实验-收益负向关闭',
  [LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasBenefitsForFullRelease]: '正式实验-有收益全量',
  [LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError]: '开发期间-实验配置错误',
  [LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange]: '开发期间-实验策略变更',

  [LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes]: '新增代码逻辑导致数据异常',
  [LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes]: '历史代码逻辑导致数据异常',
  [LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem]: '曝光代码问题导致的进组不均',
  [LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError]: '正式实验期间-实验配置错误',
  [LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges]: '其他技术问题变动',

  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect]: '实验需求变更-实验设计缺陷',
  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem]: '实验需求变更- LR 引入',
  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact]: '素材等上新运营配置耦合影响',
  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges]: '其他产品策略变动',

  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAProblem]: 'PreAA 数据异常（用户分组策略异常）',
  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_LibraProblem]: 'Libra 系统原因导致的进组不均',
  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_ThirdPartyDependency]: '三方依赖导致异常',
  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons]: '原因不明，重开观察继续分析',
  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others]: '其他',

  /** 以下为废弃字段 **/
  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_Others]: '其他（补充填写）-废弃',
  [LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightInReleaseStrategyChange]:
    '正式实验期间-实验策略变更-废弃',
  [LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAOrUnevenGroup]: 'PreAA 或进组不均-废弃',
};

export const libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap: Record<
  LibraFlightCloseReopenSubTypeCanPreInterceptType,
  string
> = {
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanNotPreIntercept]: '否，无法前置拦截',
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInDevelopmentPeriod]: '是，RD开发或自测阶段可拦截',
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInFeatureFuncTestPeriod]:
    '是，QA新功能测试阶段可拦截',
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInIntegrationPeriod]:
    '是，QA集成/系统/上线前测试阶段可拦截',
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInLibraConfigCheckPeriod]:
    '是，实验管控配置校验可拦截',
  [LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInOnlineLibraPatrolPeriod]:
    '是，线上巡检/监控可拦截',
};

export const getReopenReasonDisplayText = (
  subType: LibraFlightCloseReopenSubType,
  detailType: LibraFlightCloseReopenSubTypeDetailType,
): string => {
  const subTypeText = libraFlightCloseAttributionReopenSubType2DisplayNameMap[subType] || '';
  const detailTypeText = libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[detailType] || '';
  return `${subTypeText}/${detailTypeText}`;
};

export const libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap: Record<
  LibraFlightCloseFullReleaseSubType,
  string
> = {
  [LibraFlightCloseFullReleaseSubType.Normal]: '正常全量',
  [LibraFlightCloseFullReleaseSubType.Overdue]: '超期全量',
};

export const libraFlightCloseAttributionOfflineSubTypeOrder = [
  LibraFlightCloseOfflineSubType.IndicatorNotMatch,
  LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease,
  LibraFlightCloseOfflineSubType.OnlineEmergency,
  LibraFlightCloseOfflineSubType.UserNegativeFeedback,
  LibraFlightCloseOfflineSubType.Others,
];

export const libraFlightCloseAttributionOfflineSubType2DisplayNameMap: Record<LibraFlightCloseOfflineSubType, string> =
  {
    [LibraFlightCloseOfflineSubType.IndicatorNotMatch]: '指标不符合预期或需要继续迭代',
    [LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease]: '回收数据，准备推全',
    [LibraFlightCloseOfflineSubType.OnlineEmergency]: '线上应急（指标异常、用户反馈等），关闭止损',
    [LibraFlightCloseOfflineSubType.UserNegativeFeedback]: '用户负向反馈',
    [LibraFlightCloseOfflineSubType.Others]: '其他（补充填写）',
  };

export const libraStopDetailReason2DisplayNameMap: Record<string, string> = {
  CollectData: '回收数据，准备推全',
  OnlineEmergency: '线上应急、关闭止损',
  UserNegativeFeedback: '用户负向反馈',
  IndicatorNotMatch: '指标不符合预期或需要继续迭代',

  ConfigError: '实验配置错误',
  Bugfix: 'Bug修复或问题排查',
  RequirementChange: '需求设计变更',
  FlightDesignChange: '实验设计变更',
  NonSubjective: '非主观因素',

  DependencyNotOnline: '依赖未上线(代码或三方配置等)',
  AvoidStuckDuringBanPeriod: '避免封禁期被卡住，提前走完审核流程',

  // 非Libra平台枚举
  GrayToRelease: '灰度转正式',
  DataError: '数据劣化(预期内)',
  ExpireToFull: '实验超期全量',
  ExperimentNegative: '实验负向',
  StrategyChange: '策略迭代',
  SubmitCheck: '过审实验',
  FullRelease: '实验全量',
};

export const libraType2DisplayNameMap: Record<number, string> = {
  0: '正式实验',
  1: '灰度实验',
  2: '反转实验',
  3: '长期实验',
  4: '容器实验',
  5: '策略实验',
  6: '活动实验',
  7: '线下实验(测试or调试)',
  8: '其他实验',
};

export const libraReopenTypeDisplayName = (type?: LibraReopenReasonType) => {
  if (!type) {
    return '';
  }
  return libraReopenType2DisplayNameMap[type] ?? '';
};

export const flightStatusDisplayName = (status: number) => {
  if (status === 0 || status === 91) {
    return '已结束';
  }
  if (status === 1) {
    return '进行中';
  }
  if (status === 2) {
    return '待调度';
  }
  if (status === 3) {
    return '调试中';
  }
  if (status === 4) {
    return '已暂停';
  }
  return '';
};

export const flightStatusTreeData = (): TreeEnumOptions[] => [
  {
    label: '已结束',
    value: LibraFlightStatus.Ended,
    key: `${LibraFlightStatus.Ended}`,
    children: [],
  },
  {
    label: '进行中',
    value: LibraFlightStatus.InProgress,
    key: `${LibraFlightStatus.InProgress}`,
    children: [],
  },
  {
    label: '待调度',
    value: LibraFlightStatus.ToBeScheduled,
    key: `${LibraFlightStatus.ToBeScheduled}`,
    children: [],
  },
  {
    label: '调试中',
    value: LibraFlightStatus.InDebug,
    key: `${LibraFlightStatus.InDebug}`,
    children: [],
  },
  {
    label: '已暂停',
    value: LibraFlightStatus.Paused,
    key: `${LibraFlightStatus.Paused}`,
    children: [],
  },
];

export const flightCloseAttributionTreeData = (): TreeEnumOptions[] => [
  {
    label: libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.Reopen],
    value: `${LibraFlightCloseMainType.Reopen}`,
    key: `${LibraFlightCloseMainType.Reopen}`,
    children: [
      {
        label: libraFlightCloseAttributionReopenSubType2DisplayNameMap[LibraFlightCloseReopenSubType.Normal],
        value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}`,
        key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}`,
        children: [
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.Normal_CloseGray
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_CloseGray}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_CloseGray}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasBenefitsForFullRelease
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasBenefitsForFullRelease}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasBenefitsForFullRelease}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.Normal}-${LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange}`,
            children: [],
          },
        ],
      },
      {
        label:
          libraFlightCloseAttributionReopenSubType2DisplayNameMap[LibraFlightCloseReopenSubType.CodeRelatedAnomaly],
        value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}`,
        key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}`,
        children: [
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.CodeRelatedAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges}`,
            children: [],
          },
        ],
      },
      {
        label:
          libraFlightCloseAttributionReopenSubType2DisplayNameMap[LibraFlightCloseReopenSubType.StrategyChangeAnomaly],
        value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}`,
        key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}`,
        children: [
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.StrategyChangeAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges}`,
            children: [],
          },
        ],
      },
      {
        label: libraFlightCloseAttributionReopenSubType2DisplayNameMap[LibraFlightCloseReopenSubType.OtherAnomaly],
        value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}`,
        key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}`,
        children: [
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAProblem
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAProblem}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAProblem}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_LibraProblem
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_LibraProblem}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_LibraProblem}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_ThirdPartyDependency
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_ThirdPartyDependency}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_ThirdPartyDependency}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons}`,
            children: [],
          },
          {
            label:
              libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
                LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others
              ],
            value: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others}`,
            key: `${LibraFlightCloseMainType.Reopen}-${LibraFlightCloseReopenSubType.OtherAnomaly}-${LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others}`,
            children: [],
          },
        ],
      },
    ],
  },
  {
    label: libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.FullRelease],
    value: `${LibraFlightCloseMainType.FullRelease}`,
    key: `${LibraFlightCloseMainType.FullRelease}`,
    children: [
      {
        label: libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap[LibraFlightCloseFullReleaseSubType.Normal],
        value: `${LibraFlightCloseMainType.FullRelease}-${LibraFlightCloseFullReleaseSubType.Normal}`,
        key: `${LibraFlightCloseMainType.FullRelease}-${LibraFlightCloseFullReleaseSubType.Normal}`,
        children: [],
      },
      {
        label: libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap[LibraFlightCloseFullReleaseSubType.Overdue],
        value: `${LibraFlightCloseMainType.FullRelease}-${LibraFlightCloseFullReleaseSubType.Overdue}`,
        key: `${LibraFlightCloseMainType.FullRelease}-${LibraFlightCloseFullReleaseSubType.Overdue}`,
        children: [],
      },
    ],
  },
  {
    label: libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.Offline],
    value: `${LibraFlightCloseMainType.Offline}`,
    key: `${LibraFlightCloseMainType.Offline}`,
    children: [
      {
        label:
          libraFlightCloseAttributionOfflineSubType2DisplayNameMap[
            LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease
          ],
        value: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease}`,
        key: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease}`,
        children: [],
      },
      {
        label: libraFlightCloseAttributionOfflineSubType2DisplayNameMap[LibraFlightCloseOfflineSubType.OnlineEmergency],
        value: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.OnlineEmergency}`,
        key: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.OnlineEmergency}`,
        children: [],
      },
      {
        label:
          libraFlightCloseAttributionOfflineSubType2DisplayNameMap[LibraFlightCloseOfflineSubType.UserNegativeFeedback],
        value: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.UserNegativeFeedback}`,
        key: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.UserNegativeFeedback}`,
        children: [],
      },
      {
        label:
          libraFlightCloseAttributionOfflineSubType2DisplayNameMap[LibraFlightCloseOfflineSubType.IndicatorNotMatch],
        value: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.IndicatorNotMatch}`,
        key: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.IndicatorNotMatch}`,
        children: [],
      },
      {
        label: libraFlightCloseAttributionOfflineSubType2DisplayNameMap[LibraFlightCloseOfflineSubType.Others],
        value: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.Others}`,
        key: `${LibraFlightCloseMainType.Offline}-${LibraFlightCloseOfflineSubType.Others}`,
        children: [],
      },
    ],
  },
];

export const flightTagColorArray: TagColor[] = [
  'blue',
  'amber',
  'cyan',
  'purple',
  'green',
  'grey',
  'indigo',
  'light-blue',
  'light-green',
  'lime',
  'orange',
  'pink',
  'red',
  'teal',
  'violet',
  'yellow',
  'white',
];

export const LibraNewInfoTableColumnConfigMap = (groups?: LibraNewInfoTableColumnGroup[]) => {
  const configMap: { [key: string]: LibraNewInfoTableColumnConfig } = {};
  const defaultColumnsSetting = LibraInfoColumnDefaultSetting();
  const curGroups = groups?.length ? groups : defaultColumnsSetting.groups;
  curGroups.forEach(it => {
    it.configs.forEach(config => {
      configMap[config.column_id] = config;
    });
  });
  return configMap;
};

export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    const millions = num / 1000000;
    return `${millions.toFixed(1)}m`;
  } else if (num >= 1000) {
    const thousands = num / 1000;
    return `${thousands.toFixed(1)}k`;
  } else {
    return num.toString();
  }
};

export const buildLibraCloseAttributionFilterRule = (pageStatus: LibraNewInfoTablePageStatus) => {
  const filterRules: FilterRuleDesc[] = [];
  if (!pageStatus.selectedCloseAttribution || pageStatus.selectedCloseAttribution.length === 0) {
    // 没有特定筛选，直接返回
    return filterRules;
  }

  // 如果选中的是：未填写归因，则优先处理
  if (pageStatus.selectedCloseAttribution.includes(`${LibraFlightHasNoCloseAttribution}`)) {
    filterRules.push({
      keyPath: ['flightInfo.closeAttributionInfo'],
      conditionType: ConditionType.ConditionTypeNull,
      targetValue: [],
    });
    return filterRules;
  }

  // 构建OR条件组，每个选择的归因作为一个独立的条件
  const attributionConditions: FilterRuleDesc[] = [];

  for (const attribution of pageStatus.selectedCloseAttribution) {
    // attribution 的格式有：0、0-0、0-1-201 三种格式
    const typesArray = attribution.split('-');
    if (typesArray.length === 0) {
      // 非法数据，不处理
      continue;
    }

    const mainType = Number(typesArray[0]) as LibraFlightCloseMainType;

    if (typesArray.length === 1) {
      // 只有 mainType
      attributionConditions.push({
        keyPath: ['flightInfo.closeAttributionInfo.mainType'],
        conditionType: ConditionType.ConditionTypeEqual,
        targetValue: [mainType],
      });
    } else if (typesArray.length === 2) {
      // 有 mainType 和 subType
      const subType = Number(typesArray[1]);

      if (mainType === LibraFlightCloseMainType.Reopen) {
        attributionConditions.push({
          keyPath: ['flightInfo.closeAttributionInfo.mainType', 'flightInfo.closeAttributionInfo.reopenSubType'],
          conditionType: ConditionType.ConditionTypeOneToOne,
          targetValue: [mainType, subType],
        });
      } else if (mainType === LibraFlightCloseMainType.FullRelease) {
        attributionConditions.push({
          keyPath: ['flightInfo.closeAttributionInfo.mainType', 'flightInfo.closeAttributionInfo.fullReleaseSubType'],
          conditionType: ConditionType.ConditionTypeOneToOne,
          targetValue: [mainType, subType],
        });
      } else if (mainType === LibraFlightCloseMainType.Offline) {
        attributionConditions.push({
          keyPath: ['flightInfo.closeAttributionInfo.mainType', 'flightInfo.closeAttributionInfo.offlineSubType'],
          conditionType: ConditionType.ConditionTypeOneToOne,
          targetValue: [mainType, subType],
        });
      }
    } else if (typesArray.length === 3) {
      // 有 mainType、subType 和 detailType（只有实验重开，有 detailType）
      const subType = Number(typesArray[1]);
      const detailType = Number(typesArray[2]);

      if (mainType === LibraFlightCloseMainType.Reopen) {
        attributionConditions.push({
          keyPath: [
            'flightInfo.closeAttributionInfo.mainType',
            'flightInfo.closeAttributionInfo.reopenSubType',
            'flightInfo.closeAttributionInfo.reopenSubTypeDetailType',
          ],
          conditionType: ConditionType.ConditionTypeOneToOne,
          targetValue: [mainType, subType, detailType],
        });
      }
    }
  }

  // 使用OR条件组合所有归因条件

  return attributionConditions;
};
export const dateToTimestamp = (dateStr: string): number => {
  const date = dayjs(dateStr);
  const timestampInMs = date.valueOf();
  return Math.floor(timestampInMs / 1000);
};
export const timestampToDate = (timestamp: number | undefined): string => {
  if (timestamp === undefined) {
    return '';
  }
  const date = dayjs(timestamp * 1000);
  return date.format('YYYY-MM-DD');
};
export const buildLibraFilterRules = (pageStatus: LibraNewInfoTablePageStatus, userEmail: string) => {
  const filterRules: FilterRuleDesc[] = [];

  if (pageStatus.onlyMineChecked) {
    filterRules.push({
      keyPath: ['flightInfo.owners.email'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: [userEmail],
    });
  }
  if (pageStatus.selectedAppIds && pageStatus.selectedAppIds.length > 0) {
    filterRules.push({
      keyPath: ['libraAppId'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: pageStatus.selectedAppIds.map(id => getLibraAppIdByAppId(id)),
    });
  } else {
    const allAppIds = LibraBusinessSelectOptions.map(option => getLibraAppIdByAppId(option.value));
    filterRules.push({
      keyPath: ['libraAppId'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: allAppIds,
    });
  }
  if (pageStatus.selectedOwner && pageStatus.selectedOwner.length > 0) {
    const validEmails = pageStatus.selectedOwner
      .map(owner => owner?.email ?? '')
      .filter((email): email is string => email.length > 0);
    if (validEmails.length > 0) {
      filterRules.push({
        keyPath: ['flightInfo.owners.email'],
        conditionType: ConditionType.ConditionTypeIn,
        targetValue: validEmails,
      });
    }
  }
  if (pageStatus.selectedBusinessLine?.length) {
    filterRules.push({
      keyPath: ['meegoInfo.businessLine'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: pageStatus.selectedBusinessLine,
    });
  }
  if (pageStatus.selectedBusinessTeam?.length) {
    filterRules.push({
      keyPath: ['meegoTeamInfo.teamName'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: pageStatus.selectedBusinessTeam,
    });
  }
  // 添加过滤时间的开始时间
  if (
    pageStatus.selectedFlightTimeType === LibraTimeQueryType.OnlyStartRange ||
    pageStatus.selectedFlightTimeType === LibraTimeQueryType.BothStartAndEndRange
  ) {
    if (pageStatus.selectedFlightStartTime) {
      filterRules.push({
        keyPath: ['flightInfo.startTime'],
        conditionType: ConditionType.ConditionTypeGTE,
        targetValue: [pageStatus.selectedFlightStartTime],
      });
    }
  }
  if (pageStatus.selectedFlightTimeType === LibraTimeQueryType.OnlyStartRange) {
    if (pageStatus.selectedFlightEndTime) {
      filterRules.push({
        keyPath: ['flightInfo.startTime'],
        conditionType: ConditionType.ConditionTypeLTE,
        targetValue: [pageStatus.selectedFlightEndTime],
      });
    }
  }
  // 添加过滤时间的结束时间
  if (
    pageStatus.selectedFlightTimeType === LibraTimeQueryType.OnlyEndRange ||
    pageStatus.selectedFlightTimeType === LibraTimeQueryType.BothStartAndEndRange
  ) {
    if (pageStatus.selectedFlightEndTime) {
      filterRules.push({
        keyPath: ['flightInfo.endTime'],
        conditionType: ConditionType.ConditionTypeLTE,
        targetValue: [pageStatus.selectedFlightEndTime],
      });
    }
  }

  if (pageStatus.selectedFlightTimeType === LibraTimeQueryType.OnlyEndRange) {
    if (pageStatus.selectedFlightStartTime) {
      filterRules.push({
        keyPath: ['flightInfo.endTime'],
        conditionType: ConditionType.ConditionTypeGTE,
        targetValue: [pageStatus.selectedFlightStartTime],
      });
    }
  }
  let closeAttributionFilterQuery: Record<string, any> = {};
  if (pageStatus.selectedCloseAttribution) {
    const closeAttributionFilterRules = buildLibraCloseAttributionFilterRule(pageStatus);
    if (closeAttributionFilterRules.length > 0) {
      closeAttributionFilterQuery = generateFilterRuleQuery(FilterGroupType.Or, closeAttributionFilterRules);
    }
  }
  if (pageStatus.selectedFlightStatus?.length) {
    // 如果 values 中包含 LibraFlightStatus.Ended，则也需要添加 LibraFlightStatus.Released
    const selectedFlightStatusValues = [...pageStatus.selectedFlightStatus];
    if (selectedFlightStatusValues.includes(LibraFlightStatus.Ended)) {
      selectedFlightStatusValues.push(LibraFlightStatus.Released);
    }
    filterRules.push({
      keyPath: ['flightInfo.status'],
      conditionType: ConditionType.ConditionTypeIn,
      targetValue: selectedFlightStatusValues,
    });
  }
  if (pageStatus.filter_config?.filter_id && pageStatus.filter_config.filter_value?.length) {
    const filterConfig = LibraInfoColumnFilterConfigs().find(
      it => it.filter_id === pageStatus.filter_config?.filter_id,
    );
    let targetValue: string[] | number[] = pageStatus.filter_config.filter_value.split(',');
    if (filterConfig?.value_type === FilterOptionType.TypeNumber) {
      targetValue = targetValue.map(it => Number(it));
    }
    let type = ConditionType.ConditionTypeContain;
    if (filterConfig?.value_type === FilterOptionType.TypeNumber) {
      type = ConditionType.ConditionTypeIn;
    }
    filterRules.push({
      keyPath: filterConfig?.key_path ?? [],
      conditionType: type,
      targetValue: [...targetValue],
    });
  }

  const filterQuerys = generateFilterRuleQuery(FilterGroupType.And, filterRules);
  return {
    $and: [filterQuerys, closeAttributionFilterQuery],
  };
};

export const LibraRoleType2DisplayNameMap: Record<LibraPlatformRoleType, string> = {
  [LibraPlatformRoleType.Guest]: '访客',
  [LibraPlatformRoleType.NomalUser]: '普通用户',
  [LibraPlatformRoleType.ProductUser]: '产品成员',
  [LibraPlatformRoleType.Admin]: '管理员',
};

// 根据 MeegoTeamStr 取前 12 位（若没有 12 位则有多少位取多少位），返回整型
export const MeegoTeamShortIdByMeegoTeamStr = (meegoTeamStr: string) => {
  const fixLength = 12;
  let res = 0;
  if (meegoTeamStr.length < fixLength) {
    res = Number(meegoTeamStr);
  } else {
    res = Number(meegoTeamStr.substring(0, fixLength));
  }
  if (!isNaN(res)) {
    return res;
  }
  return 0;
};

// 一些固定的 Meego Id 短类型（截取前 11 位）
// 背景：meego 返回的原始 teamId 是 7495787514574438428 这种长度的 id, `number` 类型的精度承载不了，纸飞机自己转换一下
// CapCut-多轨工具-客户端
export const CapCutClientVideoToolShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495787514574438428');
// CC-内容生态-移动端
export const CapCutContentEcosystemShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495793202868355076');
// CC-内容生态-Server
export const CapCutContentEcosystemServerShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7517634109317971972');
// 剪映-内容生态-Server
export const LVContentEcosystemServerShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7517652228325392403');
// 剪C-商业化-服务端-订阅
export const LVCCCommerceServerSubscribeShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495753363892617217');
// 剪C-商业化-客户端/前端-订阅
export const LVCCCommerceClientFESubscribeShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495794016559169540');
// 剪C-商业化-服务端-广告
export const LVCCCommerceServerAdShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495794016575963139');
// 剪C-商业化-客户端/前端-广告
export const LVCCCommerceClientFEAdShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7495767403159191571');
// 多媒体-特效&架构
export const LVCCMultimediaEffectAndArchShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7509127200730267649');
// 图像工具-主框架
export const RetouchImageToolMainFrameShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7527659486123753474');
// 图像工具-模板
export const RetouchImageToolTemplateShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7527591717374132225');
// 图像工具-工具
export const RetouchImageToolToolShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7527584917794881564');
// 图像工具-Server
export const RetouchImageToolServerShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7527579237147017218');
// 图像工具-推荐
export const RetouchImageToolRecommendShortMeegoId = MeegoTeamShortIdByMeegoTeamStr('7527579153952980993');

export enum MeegoTeamType {
  LV_Mobile = 1,
  CC_Mobile = 2,
  LV_PC = 3,
  CC_PC = 4,
  LV_Web = 5,
  CC_Web = 6,
  LV_Server = 7,
  CC_Server = 8,
  Other = 9,
  Commerce = 10, // 商业化，单独补充一类（同时包含 剪映 和 CC）
  Multimedia = 11, // 多媒体，单独补充一类（同时包含 剪映 和 CC）
  Retouch = 12, // 醒图
  // Hypic = 13, // Hypic（Hypic 暂时为空，都归到醒图，因为醒&H暂时还没有分团队）
}

// 剪映移动端团队
export const MeegoTeamIdsForJianyingMobile: number[] = [
  38624, // 【剪映】-移动端-音视频-统计-实验（剪映-视频编辑器-移动端-音视频）
  38622, // 素材与内容-剪映（剪映-视频编辑器-移动端-素材）
  38612, // 智能剪辑实验同步群（剪映-视频编辑器-移动端-智能剪辑）-- 剪映-视频编辑器-移动端-智能剪辑
  38603, // 【无需关注】【剪映】-主框架-统计 -- 剪映-视频编辑器-移动端-主框架
  38600, // 【无需关注】【剪映】-基础剪辑-统计（持续更新）-- 剪映-视频编辑器-移动端-基础剪辑
  38227, // 剪映内容生态（剪映-内容生态-移动端）
];

// CapCut 移动端团队
export const MeegoTeamIdsForCapCutMobile: number[] = [
  CapCutClientVideoToolShortMeegoId, // CapCut-多轨工具-客户端
  37766, // CapCut主框架&基础产品
  CapCutContentEcosystemShortMeegoId, // CC-内容生态-移动端
];

// 剪映 PC 团队
export const MeegoTeamIdsForJianyingPC: number[] = [
  39067, // 剪映PC
];

// CapCut PC 团队
export const MeegoTeamIdsForCapCutPC: number[] = [
  39068, // CCPC
];

// 剪映 Web 团队
export const MeegoTeamIdsForJianyingWeb: number[] = [
  39189, // 剪映 Web 前端
];

// CapCut Web 团队
export const MeegoTeamIdsForCapCutWeb: number[] = [
  39188, // Capcut Web 前端
];

// 剪映 Server 团队
export const MeegoTeamIdsForJianyingServer: number[] = [
  39000, // 剪C-视频工具-Server
  39025, // 剪映-主框架-服务端
  CapCutContentEcosystemServerShortMeegoId, // CC-内容生态-Server
  LVContentEcosystemServerShortMeegoId, // 剪映-内容生态-Server
];

// 商业化团队（订阅+广告）
export const MeegoTeamIdsForCommerce: number[] = [
  LVCCCommerceServerSubscribeShortMeegoId, // 剪C-商业化-服务端-订阅
  LVCCCommerceClientFESubscribeShortMeegoId, // 剪C-商业化-客户端/前端-订阅
  LVCCCommerceServerAdShortMeegoId, // 剪C-商业化-服务端-广告
  LVCCCommerceClientFEAdShortMeegoId, // 剪C-商业化-客户端/前端-广告
];

// 多媒体团队
export const MeegoTeamIdsForMultimedia: number[] = [
  LVCCMultimediaEffectAndArchShortMeegoId, // 多媒体-特效&架构
];

// 醒图团队
export const MeegoTeamIdsForRetouch: number[] = [
  RetouchImageToolMainFrameShortMeegoId, // 图像工具-主框架
  RetouchImageToolTemplateShortMeegoId, // 图像工具-模板
  RetouchImageToolToolShortMeegoId, // 图像工具-工具
  RetouchImageToolServerShortMeegoId, // 图像工具-server
  RetouchImageToolRecommendShortMeegoId, // 图像工具-推荐
];

// Hypic 团队
// export const MeegoTeamIdsForHypic: number[] = [];

// 所有 Meego 团队汇总（移动端 + PC + Web）
export const MeegoTeamIds: number[] = [
  ...MeegoTeamIdsForJianyingMobile,
  ...MeegoTeamIdsForCapCutMobile,
  ...MeegoTeamIdsForJianyingPC,
  ...MeegoTeamIdsForCapCutPC,
  ...MeegoTeamIdsForJianyingWeb,
  ...MeegoTeamIdsForCapCutWeb,
  ...MeegoTeamIdsForJianyingServer,
  ...MeegoTeamIdsForCommerce,
  ...MeegoTeamIdsForMultimedia,
  ...MeegoTeamIdsForRetouch,
];

// 通过团队类型获取团队 ID 列表
export const MeegoTeamIdsByMeegoTeamType = (type: MeegoTeamType) => {
  if (type === MeegoTeamType.LV_Mobile) {
    return MeegoTeamIdsForJianyingMobile;
  } else if (type === MeegoTeamType.CC_Mobile) {
    return MeegoTeamIdsForCapCutMobile;
  } else if (type === MeegoTeamType.LV_PC) {
    return MeegoTeamIdsForJianyingPC;
  } else if (type === MeegoTeamType.CC_PC) {
    return MeegoTeamIdsForCapCutPC;
  } else if (type === MeegoTeamType.LV_Web) {
    return MeegoTeamIdsForJianyingWeb;
  } else if (type === MeegoTeamType.CC_Web) {
    return MeegoTeamIdsForCapCutWeb;
  } else if (type === MeegoTeamType.LV_Server) {
    return MeegoTeamIdsForJianyingServer;
  } else if (type === MeegoTeamType.Commerce) {
    return MeegoTeamIdsForCommerce;
  } else if (type === MeegoTeamType.Multimedia) {
    return MeegoTeamIdsForMultimedia;
  } else if (type === MeegoTeamType.Retouch) {
    return MeegoTeamIdsForRetouch;
  }
  return [];
};

// 通过 TeamID 查找团队名称
export const MeegoTeamNameByTeamId = (teamId: number) => {
  if (teamId === 38624) {
    return '剪映-视频编辑器-移动端-音视频';
  } else if (teamId === 38622) {
    return '剪映-视频编辑器-移动端-素材';
  } else if (teamId === 38612) {
    return '剪映-视频编辑器-移动端-智能剪辑';
  } else if (teamId === 38603) {
    return '剪映-视频编辑器-移动端-主框架';
  } else if (teamId === 38600) {
    return '剪映-视频编辑器-移动端-基础剪辑';
  } else if (teamId === 38227) {
    return '剪映-内容生态-移动端';
  } else if (teamId === 39067) {
    return '剪映PC';
  } else if (teamId === 39068) {
    return 'CCPC';
  } else if (teamId === 39189) {
    return '剪映 Web 前端';
  } else if (teamId === 39188) {
    return 'Capcut Web 前端';
  } else if (teamId === 39000) {
    return '剪C-视频工具-Server';
  } else if (teamId === 39025) {
    return '剪映-主框架-服务端';
  } else if (teamId === CapCutClientVideoToolShortMeegoId) {
    return 'CapCut-多轨工具-客户端';
  } else if (teamId === 37766) {
    return 'CapCut主框架&基础产品';
  } else if (teamId === CapCutContentEcosystemShortMeegoId) {
    return 'CC-内容生态-移动端';
  } else if (teamId === LVCCCommerceServerSubscribeShortMeegoId) {
    return '剪C-商业化-服务端-订阅';
  } else if (teamId === LVCCCommerceClientFESubscribeShortMeegoId) {
    return '剪C-商业化-客户端/前端-订阅';
  } else if (teamId === LVCCCommerceServerAdShortMeegoId) {
    return '剪C-商业化-服务端-广告';
  } else if (teamId === LVCCCommerceClientFEAdShortMeegoId) {
    return '剪C-商业化-客户端/前端-广告';
  } else if (teamId === LVCCMultimediaEffectAndArchShortMeegoId) {
    return '多媒体-特效&架构';
  } else if (teamId === CapCutContentEcosystemServerShortMeegoId) {
    return 'CC-内容生态-Server';
  } else if (teamId === LVContentEcosystemServerShortMeegoId) {
    return '剪映-内容生态-Server';
  } else if (teamId === RetouchImageToolMainFrameShortMeegoId) {
    return '图像工具-主框架';
  } else if (teamId === RetouchImageToolTemplateShortMeegoId) {
    return '图像工具-模板';
  } else if (teamId === RetouchImageToolToolShortMeegoId) {
    return '图像工具-工具';
  } else if (teamId === RetouchImageToolServerShortMeegoId) {
    return '图像工具-Server';
  } else if (teamId === RetouchImageToolRecommendShortMeegoId) {
    return '图像工具-推荐';
  }
  return '';
};

export const MeegoTeamMapForUISelect: Record<string, string> = {
  '38624': '剪映-视频编辑器-移动端-音视频',
  '38622': '剪映-视频编辑器-移动端-素材',
  '38612': '剪映-视频编辑器-移动端-智能剪辑',
  '38603': '剪映-视频编辑器-移动端-主框架',
  '38600': '剪映-视频编辑器-移动端-基础剪辑',
  '38227': '剪映-内容生态-移动端',
  '39067': '剪映PC',
  '39068': 'CCPC',
  '39188': 'Capcut Web 前端',
  '39189': '剪映 Web 前端',
  '39000': '剪C-视频工具-Server',
  '39025': '剪映-主框架-服务端',
  [CapCutClientVideoToolShortMeegoId]: 'CapCut-多轨工具-客户端',
  '37766': 'CapCut主框架&基础产品',
  [CapCutContentEcosystemShortMeegoId]: 'CC-内容生态-移动端',
  [LVCCCommerceServerSubscribeShortMeegoId]: '剪C-商业化-服务端-订阅',
  [LVCCCommerceClientFESubscribeShortMeegoId]: '剪C-商业化-客户端/前端-订阅',
  [LVCCCommerceServerAdShortMeegoId]: '剪C-商业化-服务端-广告',
  [LVCCCommerceClientFEAdShortMeegoId]: '剪C-商业化-客户端/前端-广告',
  [LVCCMultimediaEffectAndArchShortMeegoId]: '多媒体-特效&架构',
  [CapCutContentEcosystemServerShortMeegoId]: 'CC-内容生态-Server',
  [LVContentEcosystemServerShortMeegoId]: '剪映-内容生态-Server',
  [RetouchImageToolMainFrameShortMeegoId]: '图像工具-主框架',
  [RetouchImageToolTemplateShortMeegoId]: '图像工具-模板',
  [RetouchImageToolToolShortMeegoId]: '图像工具-工具',
  [RetouchImageToolServerShortMeegoId]: '图像工具-Server',
  [RetouchImageToolRecommendShortMeegoId]: '图像工具-推荐',
};

export const checkIfTeamIdMatchLibraRegion = (teamId: number, region: LibraRegion) => {
  // 如果是 CN 区域，则 teamId 必须是剪映相关团队
  if (region === LibraRegion.CN) {
    if (MeegoTeamIdsForJianyingMobile.includes(teamId)) {
      return true;
    }
    if (MeegoTeamIdsForJianyingPC.includes(teamId)) {
      return true;
    }
    if (MeegoTeamIdsForJianyingWeb.includes(teamId)) {
      return true;
    }

    if (teamId === 39025) {
      // 剪映-主框架-服务端
      return true;
    }
  }

  // 如果是 SG 区域，则 teamId 必须是 CapCut 相关团队
  if (region === LibraRegion.SG) {
    if (MeegoTeamIdsForCapCutMobile.includes(teamId)) {
      return true;
    }
    if (MeegoTeamIdsForCapCutPC.includes(teamId)) {
      return true;
    }
    if (MeegoTeamIdsForCapCutWeb.includes(teamId)) {
      return true;
    }
  }

  if (MeegoTeamIdsForCommerce.includes(teamId)) {
    // 商业化：剪映 + CapCut 都包括，返回 true
    return true;
  }

  if (MeegoTeamIdsForMultimedia.includes(teamId)) {
    // 多媒体：剪映 + CapCut 都包括，返回 true
    return true;
  }

  if (
    teamId === 39000 ||
    teamId === CapCutContentEcosystemServerShortMeegoId ||
    teamId === LVContentEcosystemServerShortMeegoId
  ) {
    // 剪C-视频工具-Server：剪映 + CapCut 都包括，返回 true
    // 剪映-内容生态-Server：剪映 + CapCut 都包括，返回 true
    // CC-内容生态-Server：剪映 + CapCut 都包括，返回 true
    return true;
  }

  if (MeegoTeamIdsForRetouch.includes(teamId)) {
    // 醒图 + Hypic 都包括（目前醒&H 还未拆分团队，不像剪&C）
    return true;
  }

  return false;
};

// 实验变更固定关注人（通过虚拟业务团队映射）
export const LibraChangeNotifyCardFixedObservers = (teamId: number) => {
  if (MeegoTeamIdsForJianyingMobile.includes(teamId)) {
    return ['<EMAIL>'];
  }
  if (MeegoTeamIdsForCapCutWeb.includes(teamId)) {
    return ['<EMAIL>', '<EMAIL>'];
  }

  if (MeegoTeamIdsForRetouch.includes(teamId)) {
    return ['<EMAIL>'];
  }

  return [];
};

// 获取实验通知发送的话题群（固定配置）
export const LarkChatIdsByMeegoTeamType = (type: MeegoTeamType) => {
  if (type === MeegoTeamType.LV_Mobile) {
    // 剪映-移动端实验，统一发送至话题群：【官方唯一】剪映(移动端)-实验话题群(监控)
    // https://bytedance.larkoffice.com/wiki/Lp7Jwwz7XiVygWkPuVNcAU8rnUg#QDcHdx32CoztDyxwMfmcohvinEd
    return ['oc_5a7a51ef9c0daaa9e8da56eb879cfa0c'];
  } else if (type === MeegoTeamType.CC_Mobile) {
    // CapCut-移动端实验，统一发送至话题群：【官方唯一】CapCut（移动端）-实验话题群（监控）
    return ['oc_01183a997d1b0d0cd24179936df9af13'];
  } else if (type === MeegoTeamType.LV_PC) {
    // 剪映-PC 实验，统一发送至话题群：oc_9267112676ddfd296c8f6b7075b42f1c
    return ['oc_9267112676ddfd296c8f6b7075b42f1c'];
  } else if (type === MeegoTeamType.CC_PC) {
    // CapCut-PC 实验，统一发送至话题群：oc_9267112676ddfd296c8f6b7075b42f1c
    return ['oc_9267112676ddfd296c8f6b7075b42f1c'];
  } else if (type === MeegoTeamType.LV_Web) {
    // 剪映-Web 实验，统一发送至话题群：【官方唯一】剪映 Web - 实验话题群
    // https://bytedance.larkoffice.com/docx/PAVoduWEConVxpxx2YUc1OSwnEb
    return ['oc_c0dc01514406f88c37353e759cc5291d'];
  } else if (type === MeegoTeamType.CC_Web) {
    // CapCut-Web 实验，统一发送至话题群：【官方唯一】Capcut Web - 实验话题群
    return ['oc_5f5b0f4d6077ab72d398a3453844de1a'];
  } else if (type === MeegoTeamType.LV_Server) {
    // Server 实验，统一发送至话题群：剪C-服务端实验变更通知
    return ['oc_5acd147eae04780c1cab85177894dcdc'];
  } else if (type === MeegoTeamType.Commerce) {
    // 商业化团队，统一发送至话题群：剪映CapCut商业化实验同步群
    // return ['oc_37e13407ed26ee4a68bb8a8f35e8e607'];
    return []; // @FIXME 暂时不开启
  } else if (type === MeegoTeamType.Multimedia) {
    // 多媒体团队，统一发送至话题群：【官方唯一】多媒体特效-实验变更周知群
    return ['oc_222fe40d76700d6d21b9287efd4c2d21'];
  } else if (type === MeegoTeamType.Retouch) {
    // 醒图团队，统一发送至话题群：【官方唯一】醒图&hypic-实验变更周知群
    return ['oc_85f7e6e468371971e305ba4ad05872be'];
  }

  // 其他默认为 空chatId
  return [];
};

// 获取实验通知发送的话题群（通过 MeegoTeam 信息）
export const LarkChatIdsByMeegoTeamInfo = (libraInfo: LibraNewInfo | null) => {
  if (!libraInfo) {
    return [];
  }
  const meegoTeamInfos = libraInfo.meegoTeamInfo;
  if (!meegoTeamInfos || meegoTeamInfos.length === 0) {
    return [];
  }
  for (const meegoTeam of meegoTeamInfos) {
    if (MeegoTeamIdsForJianyingMobile.includes(meegoTeam.teamId) && libraInfo.libraAppId === 147) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Mobile);
    }
    if (MeegoTeamIdsForCapCutMobile.includes(meegoTeam.teamId) && libraInfo.libraAppId === 305) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.CC_Mobile);
    }
    if (MeegoTeamIdsForJianyingPC.includes(meegoTeam.teamId) && libraInfo.libraAppId === 399) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_PC);
    }
    if (
      MeegoTeamIdsForCapCutPC.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 360 || libraInfo.libraAppId === 368)
    ) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.CC_PC);
    }
    if (
      MeegoTeamIdsForCapCutWeb.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 381 || libraInfo.libraAppId === 360 || libraInfo.libraAppId === 305)
    ) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.CC_Web);
    }
    if (
      MeegoTeamIdsForJianyingWeb.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 1071 || libraInfo.libraAppId === 399 || libraInfo.libraAppId === 147)
    ) {
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Web);
    }
    if (MeegoTeamIdsForJianyingServer.includes(meegoTeam.teamId)) {
      if (
        (meegoTeam.teamId === 39000 ||
          meegoTeam.teamId === CapCutContentEcosystemServerShortMeegoId ||
          meegoTeam.teamId === LVContentEcosystemServerShortMeegoId) &&
        (libraInfo.libraAppId === 147 || libraInfo.libraAppId === 305 || libraInfo.libraAppId === 295)
      ) {
        // 剪C-视频工具-Server（剪映和 CapCut 都包含）
        // 剪映-内容生态-Server（剪映和 CapCut 都包含）
        // CC-内容生态-Server（剪映和 CapCut 都包含）
        return LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Server);
      }

      if (libraInfo.libraAppId === 147) {
        // 其余情况，均是返回剪映实验（过滤 CapCut）
        return LarkChatIdsByMeegoTeamType(MeegoTeamType.LV_Server);
      }
    }
    if (
      MeegoTeamIdsForCommerce.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 147 ||
        libraInfo.libraAppId === 305 ||
        libraInfo.libraAppId === 255 ||
        libraInfo.libraAppId === 399 ||
        libraInfo.libraAppId === 360)
    ) {
      // 商业化团队
      // 剪映: 147
      // CapCut：305
      // 醒图：255
      // PC-剪映：399
      // PC-CapCut：360
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.Commerce);
    }
    if (
      MeegoTeamIdsForMultimedia.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 147 ||
        libraInfo.libraAppId === 305 ||
        libraInfo.libraAppId === 399 ||
        libraInfo.libraAppId === 360)
    ) {
      // 多媒体团队
      // 剪映: 147
      // CapCut：305
      // PC-剪映：399
      // PC-CapCut：360
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.Multimedia);
    }

    if (
      MeegoTeamIdsForRetouch.includes(meegoTeam.teamId) &&
      (libraInfo.libraAppId === 255 || libraInfo.libraAppId === 367 || libraInfo.libraAppId === 371)
    ) {
      // 醒图团队
      // 醒图 App: 255
      // Hypic App：367(sg) 371(va)
      return LarkChatIdsByMeegoTeamType(MeegoTeamType.Retouch);
    }
  }
  return [];
};

// Meego 团队 POC 负责人
export const MeegoTeamFixedPOCs: { [key: number]: string[] } = {
  // 剪映-视频编辑器-移动端-音视频
  38624: ['<EMAIL>'],
  // 剪映-视频编辑器-移动端-素材
  38622: ['<EMAIL>'],
  // 剪映-视频编辑器-移动端-智能剪辑
  38612: ['<EMAIL>'],
  // 剪映-视频编辑器-移动端-主框架
  38603: ['<EMAIL>'],
  // 剪映-视频编辑器-移动端-基础剪辑
  38600: ['<EMAIL>'],
  // 剪映-内容生态-移动端
  38227: ['<EMAIL>'],
  //  Capcut Web 前端
  39188: ['<EMAIL>'],
  // 剪映 Web 前端
  39189: ['<EMAIL>'],
  // 剪C-视频工具-Server
  39000: ['<EMAIL>'],
  // 剪映-主框架-服务端
  39025: ['<EMAIL>'],
  // 剪映 PC 团队
  39067: ['<EMAIL>'],
  // CapCut PC 团队
  39068: ['<EMAIL>'],
  // CapCut-多轨工具-客户端
  [CapCutClientVideoToolShortMeegoId]: ['<EMAIL>'],
  // CapCut主框架&基础产品
  37766: ['<EMAIL>'],
  // CC-内容生态-移动端
  [CapCutContentEcosystemShortMeegoId]: ['<EMAIL>', '<EMAIL>'],
  // 剪C-商业化-服务端-订阅
  [LVCCCommerceServerSubscribeShortMeegoId]: ['<EMAIL>'],
  // 剪C-商业化-客户端/前端-订阅
  [LVCCCommerceClientFESubscribeShortMeegoId]: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪C-商业化-服务端-广告
  [LVCCCommerceServerAdShortMeegoId]: ['<EMAIL>'],
  // 剪C-商业化-客户端/前端-广告
  [LVCCCommerceClientFEAdShortMeegoId]: ['<EMAIL>'],
  // 多媒体-特效&架构
  [LVCCMultimediaEffectAndArchShortMeegoId]: ['<EMAIL>'],
  // CC-内容生态-Server
  [CapCutContentEcosystemServerShortMeegoId]: ['<EMAIL>'],
  // 剪映-内容生态-Server
  [LVContentEcosystemServerShortMeegoId]: ['<EMAIL>'],
  // 图像工具-主框架
  [RetouchImageToolMainFrameShortMeegoId]: ['<EMAIL>'],
  // 图像工具-模板
  [RetouchImageToolTemplateShortMeegoId]: ['<EMAIL>'],
  // 图像工具-工具
  [RetouchImageToolToolShortMeegoId]: ['<EMAIL>'],
  // 图像工具-Server
  [RetouchImageToolServerShortMeegoId]: ['<EMAIL>'],
  // 图像工具-推荐
  [RetouchImageToolRecommendShortMeegoId]: ['<EMAIL>'],
};

// Meego 团队中排除部分成员（主要是排除通用管理员）
export const MeegoTeamExcludeMembers: { [key: number]: string[] } = {
  // 剪映-视频编辑器-移动端-音视频
  38624: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪映-视频编辑器-移动端-素材
  38622: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪映-视频编辑器-移动端-智能剪辑
  38612: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪映-视频编辑器-移动端-主框架
  38603: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪映-视频编辑器-移动端-基础剪辑
  38600: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
  // 剪映-内容生态-移动端
  38227: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // Capcut Web 前端
  39188: ['<EMAIL>', '<EMAIL>'],
  // 剪映 Web 前端
  39189: ['<EMAIL>', '<EMAIL>'],
  // 剪C-视频工具-Server
  39000: ['<EMAIL>'],
  // 剪映-主框架-服务端
  39025: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  // 剪映 PC 团队
  39067: ['<EMAIL>', '<EMAIL>'],
  // CapCut PC 团队
  39068: ['<EMAIL>', '<EMAIL>'],
  // CapCut-多轨工具-客户端
  [CapCutClientVideoToolShortMeegoId]: ['<EMAIL>'],
  // CapCut主框架&基础产品
  37766: ['<EMAIL>'],
  // CC-内容生态-移动端
  [CapCutContentEcosystemShortMeegoId]: ['<EMAIL>', '<EMAIL>'],
  // 剪C-商业化-服务端-订阅
  [LVCCCommerceServerSubscribeShortMeegoId]: ['<EMAIL>'],
  // 剪C-商业化-客户端/前端-订阅
  [LVCCCommerceClientFESubscribeShortMeegoId]: ['<EMAIL>'],
  // 剪C-商业化-服务端-广告
  [LVCCCommerceServerAdShortMeegoId]: ['<EMAIL>'],
  // 剪C-商业化-客户端/前端-广告
  [LVCCCommerceClientFEAdShortMeegoId]: ['<EMAIL>'],
  // 多媒体-特效&架构
  [LVCCMultimediaEffectAndArchShortMeegoId]: ['<EMAIL>'],
  // CC-内容生态-Server
  [CapCutContentEcosystemServerShortMeegoId]: ['<EMAIL>'],
  // 剪映-内容生态-Server
  [LVContentEcosystemServerShortMeegoId]: ['<EMAIL>'],
  // 图像工具-主框架
  [RetouchImageToolMainFrameShortMeegoId]: ['<EMAIL>'],
  // 图像工具-模板
  [RetouchImageToolTemplateShortMeegoId]: ['<EMAIL>'],
  // 图像工具-工具
  [RetouchImageToolToolShortMeegoId]: ['<EMAIL>'],
  // 图像工具-Server
  [RetouchImageToolServerShortMeegoId]: ['<EMAIL>'],
  // 图像工具-推荐
  [RetouchImageToolRecommendShortMeegoId]: ['<EMAIL>'],
};

// 通过组织架构信息获取对应的 Meego Team（有可能获取不到，这个映射靠人工维护）
export const GetLibraMeegoTeamIdByDepartmentName = (departmentName: string, libraRegion: LibraRegion) => {
  if (departmentName.includes('剪映CapCut-移动端-视频编辑器-音视频')) {
    return 38624; // 剪映-视频编辑器-移动端-音视频
  }

  if (departmentName.includes('剪映CapCut-移动端-视频编辑器-素材与内容')) {
    return 38622; // 剪映-视频编辑器-移动端-素材
  }

  if (departmentName.includes('剪映CapCut-移动端-视频编辑器-智能剪辑')) {
    return 38612; // 剪映-视频编辑器-移动端-智能剪辑
  }

  if (departmentName.includes('剪映CapCut-移动端-主框架与基础产品')) {
    return 38603; // 剪映-视频编辑器-移动端-主框架
  }

  if (departmentName.includes('剪映CapCut-移动端-视频编辑器-基础剪辑')) {
    return 38600; // 剪映-视频编辑器-移动端-基础剪辑
  }

  if (departmentName.includes('剪映CapCut-移动端-内容与分发')) {
    return 38227; // 剪映-内容生态-移动端
  }

  if (departmentName.includes('剪映CapCut-PC端')) {
    if (libraRegion === LibraRegion.CN) {
      return 39067; // 剪映 PC 团队
    } else {
      return 39068; // CapCut PC 团队
    }
  }

  if (departmentName.includes('剪映CapCut-前端-视频与图像编辑器')) {
    if (libraRegion === LibraRegion.SG) {
      return 39188; // Capcut Web 前端
    } else {
      return 39189; // 剪映 Web 前端
    }
  }

  if (departmentName.includes('剪映CapCut-策略与工程-视频编辑器')) {
    return 39000; // 剪C-视频工具-Server
  }

  if (
    departmentName.includes('产品研发和工程架构-剪映CapCut-策略与工程-内容与分发') &&
    libraRegion === LibraRegion.SG
  ) {
    return CapCutContentEcosystemServerShortMeegoId; // CC-内容生态-Server
  }

  if (
    departmentName.includes('产品研发和工程架构-剪映CapCut-策略与工程-内容与分发') &&
    libraRegion === LibraRegion.CN
  ) {
    return LVContentEcosystemServerShortMeegoId; // 剪映-内容生态-Server
  }

  if (departmentName.includes('剪映CapCut-策略与工程-主框架与基础产品')) {
    return 39025; // 剪映-主框架-服务端
  }

  if (
    (departmentName.includes('剪映CapCut-移动端-视频编辑器-智能剪辑') ||
      departmentName.includes('剪映CapCut-移动端-视频编辑器-基础剪辑') ||
      departmentName.includes('剪映CapCut-移动端-视频编辑器-素材与内容')) &&
    libraRegion !== LibraRegion.CN
  ) {
    return CapCutClientVideoToolShortMeegoId; // CapCut-多轨工具-客户端
  }

  if (
    departmentName.includes('产品研发和工程架构-剪映CapCut-移动端-主框架与基础产品') &&
    libraRegion !== LibraRegion.CN
  ) {
    return 37766; // CapCut主框架&基础产品
  }

  if (departmentName.includes('产品研发和工程架构-剪映CapCut-移动端-内容与分发') && libraRegion !== LibraRegion.CN) {
    return CapCutContentEcosystemShortMeegoId; // CC-内容生态-移动端
  }

  if (departmentName.includes('剪映CapCut-移动端-商业化-广告')) {
    return LVCCCommerceClientFEAdShortMeegoId; // 剪C-商业化-客户端/前端-广告
  }
  if (departmentName.includes('剪映CapCut-移动端-商业化-订阅')) {
    return LVCCCommerceClientFESubscribeShortMeegoId; // 剪C-商业化-客户端/前端-订阅
  }
  if (departmentName.includes('剪映CapCut-移动端-商业化-前端')) {
    // 优先划分到 “订阅”
    return LVCCCommerceClientFESubscribeShortMeegoId; // 剪C-商业化-客户端/前端-订阅
  }
  if (departmentName.includes('剪映CapCut-策略与工程-商业化')) {
    // 优先划分到 “订阅”
    return LVCCCommerceServerSubscribeShortMeegoId; // 剪C-商业化-服务端-订阅
  }
  if (
    departmentName.includes('产品研发和工程架构-剪映CapCut-多媒体-特效') ||
    departmentName.includes('产品研发和工程架构-剪映CapCut-多媒体-架构')
  ) {
    return LVCCMultimediaEffectAndArchShortMeegoId; // 多媒体-特效&架构;
  }

  if (departmentName.includes('产品研发和工程架构-剪映CapCut-移动端-醒图Hypic-主框架')) {
    // 图像工具-主框架
    return RetouchImageToolMainFrameShortMeegoId;
  }

  if (departmentName.includes('产品研发和工程架构-剪映CapCut-移动端-醒图Hypic-模板')) {
    // 图像工具-模板
    return RetouchImageToolTemplateShortMeegoId;
  }

  if (departmentName.includes('产品研发和工程架构-剪映CapCut-移动端-醒图Hypic-工具')) {
    // 图像工具-工具
    return RetouchImageToolToolShortMeegoId;
  }

  if (departmentName.includes('产品研发和工程架构-剪映CapCut-策略与工程-图像编辑器')) {
    // 图像工具-server
    return RetouchImageToolServerShortMeegoId;
  }

  if (departmentName.includes('Data-剪映CapCut-推荐')) {
    // 图像工具-推荐
    return RetouchImageToolRecommendShortMeegoId;
  }

  return 0;
};

export const MeegoProjectKeyFaceU = 'faceu';

// 获取昨天日期的起始和结束时间戳（由于话题群推送），对于周一的昨天会计算为上周五
export const YesterdayInfoForLibraTopicGroups = () => {
  // 引入插件和时区配置
  dayjs.extend(utc);
  dayjs.extend(timezone);

  // 获取当前日期是星期几（0 - 6，0 表示周日）
  const currentDay = dayjs().tz('Asia/Shanghai').day();
  let dayDiff = 1;
  if (currentDay === 1) {
    // 如果今天是周一，那么昨日是上周五
    dayDiff = 3;
  }

  // 计算昨日00:00:00时间戳
  const startOfYesterday = dayjs().tz('Asia/Shanghai').subtract(dayDiff, 'day').startOf('day').unix();
  // 格式化为 "YYYY-MM-DD"
  const formattedYesterday = dayjs()
    .tz('Asia/Shanghai') // 指定上海时区
    .subtract(dayDiff, 'day') // 回退1天（得到昨日）
    .startOf('day') // 定位到00:00:00
    .format('YYYY-MM-DD'); // 格式化为目标字符串

  // 计算昨日23:59:59时间戳
  const endOfYesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').endOf('day').subtract(1, 'millisecond').unix();

  console.log(`时间戳区间：[${startOfYesterday}, ${endOfYesterday}]`);
  return {
    startOfYesterdayTimestamp: startOfYesterday,
    endOfYesterdayTimestamp: endOfYesterday,
    formattedYesterdayStr: formattedYesterday,
  };
};

// 获取本周日期的起始和结束时间戳（由于话题群推送），对于周一的昨天会计算为上周五
export const ThisWeekInfoForLibraTopicGroups = () => {
  // 引入插件和时区配置
  dayjs.extend(utc);
  dayjs.extend(timezone);

  // 获取当前日期是星期几（0 - 6，0 表示周日）
  const currentDay = dayjs().tz('Asia/Shanghai').day();
  let dayDiff = currentDay - 1;
  if (currentDay === 0) {
    // 如果是周日，则 dayDiff 就是前 6 天
    dayDiff = 6;
  }
  if (currentDay === 1) {
    // 如果今天是周一，那么就计算上周五、上周六、上周日这 3 天
    dayDiff = 3;
  }

  // 计算昨日00:00:00时间戳
  const startDay = dayjs().tz('Asia/Shanghai').subtract(dayDiff, 'day').startOf('day').unix();
  // 格式化为 "YYYY-MM-DD"
  const formattedStartDay = dayjs()
    .tz('Asia/Shanghai') // 指定上海时区
    .subtract(dayDiff, 'day') // 回退 n 天
    .startOf('day') // 定位到00:00:00
    .format('YYYY-MM-DD'); // 格式化为目标字符串

  // 计算昨日23:59:59时间戳
  const endDay = dayjs().tz('Asia/Shanghai').subtract(1, 'day').endOf('day').subtract(1, 'millisecond').unix();
  // 格式化为 "YYYY-MM-DD"
  const formattedEndDay = dayjs()
    .tz('Asia/Shanghai')
    .subtract(1, 'day')
    .endOf('day')
    .subtract(1, 'millisecond')
    .format('YYYY-MM-DD'); // 格式化为目标字符串

  console.log(`时间戳区间：[${startDay}, ${endDay}]`);
  return {
    startOfThisWeekTimestamp: startDay,
    endOfThisWeekTimestamp: endDay,
    thisWeekDayDuration: `${formattedStartDay} ~ ${formattedEndDay}`,
  };
};

// 获取上周日期的起始和结束时间戳（由于话题群推送）
export const LastWeekInfoForLibraTopicGroups = () => {
  // 引入插件和时区配置
  dayjs.extend(utc);
  dayjs.extend(timezone);

  const now = dayjs().tz('Asia/Shanghai');
  // 计算上周一（中国周起始）
  const lastWeekMonday = now.subtract(1, 'week').startOf('week').add(1, 'day');
  // 生成五个工作日的起止时间戳
  const timestampRanges = [];
  for (let i = 0; i < 5; i = i + 4) {
    const currentDay = lastWeekMonday.add(i, 'day');
    const start = currentDay.startOf('day').unix(); // 当天 00:00:00（秒）
    const end = currentDay.endOf('day').unix() - 1; // 当天 23:59:59（秒）

    timestampRanges.push({ start, end });
  }

  const startTimestamp = timestampRanges[0].start;
  const endTimestamp = timestampRanges[1].end;
  const formattedStartDay = dayjs.unix(startTimestamp).tz('Asia/Shanghai').format('YYYY-MM-DD');
  const formattedEndDay = dayjs.unix(endTimestamp).tz('Asia/Shanghai').format('YYYY-MM-DD');
  return {
    startOfLastWeekTimestamp: startTimestamp,
    endOfLastWeekTimestamp: endTimestamp,
    lastWeekDayDuration: `${formattedStartDay} ~ ${formattedEndDay}`,
  };
};

export enum LibraListQuerySearchParamsKey {
  FlightType = 'libra_flight_type',
  FlightValue = 'libra_flight_value',
  AppId = 'libra_business_id',
  Owners = 'libra_owners',
  BusinessTeams = 'libra_business_teams',
  BusinessLines = 'libra_business_lines',
  CloseAttribution = 'libra_close_attribution',
  StartTime = 'filter_start_time',
  EndTime = 'filter_end_time',
  TimeType = 'filter_time_type',
  FlightStatus = 'libra_flight_status',
}

export enum LibraLarkSidebarType {
  CloseAttribution = 0, // 实验关闭归因
  Unknown = -1,
}

export enum LibraLarkSidebarSearchParamsKey {
  FlightId = 'libra_flight_id',
  SidebarType = 'sidebar_type',
}

// 实验关闭归因 - 次要分类的显示 Label 前缀
export const LibraCloseAttributionSubTypeLabelPrefixByMainType = (type: LibraFlightCloseMainType) => {
  let prefix = '';
  switch (type) {
    case LibraFlightCloseMainType.Reopen:
      prefix = '重开';
      break;
    case LibraFlightCloseMainType.FullRelease:
      prefix = '全量';
      break;
    case LibraFlightCloseMainType.Offline:
      prefix = '下线';
      break;
    default:
      break;
  }
  return prefix;
};

// 确认是否是 P00 需求
export const isP00Story = (libraInfo: LibraNewInfo | null) => {
  if (!libraInfo || !libraInfo.meegoInfo) {
    return false;
  }
  for (const meegoInfo of libraInfo.meegoInfo) {
    if (meegoInfo.priority === 'P00') {
      return true;
    }
  }
  return false;
};

// 计算自然日
export interface DayDiffResult {
  totalDays: number;
  workdays: number;
}

// https://www.gov.cn/zhengce/content/202310/content_6911527.htm
export const holidayOf2024 = [
  '2024-01-01', // 元旦
  '2024-02-10',
  '2024-02-11',
  '2024-02-12',
  '2024-02-13',
  '2024-02-14',
  '2024-02-15',
  '2024-02-16',
  '2024-02-17', // 春节（含调休）
  '2024-04-04',
  '2024-04-05',
  '2024-04-06', // 清明节
  '2024-05-01',
  '2024-05-02',
  '2024-05-03',
  '2024-05-04',
  '2024-05-05', // 劳动节
  '2024-06-10', // 端午节
  '2024-09-15',
  '2024-09-16',
  '2024-09-17', // 中秋节
  '2024-10-01',
  '2024-10-02',
  '2024-10-03',
  '2024-10-04',
  '2024-10-05',
  '2024-10-06',
  '2024-10-07', // 国庆节
];

export const weekdayShouldWorkOf2024 = [
  '2024-02-04',
  '2024-02-18',
  '2024-04-07',
  '2024-04-28',
  '2024-05-11',
  '2024-09-14',
  '2024-09-29',
  '2024-10-12',
];

// https://www.gov.cn/zhengce/content/202411/content_6986382.htm
export const holidayOf2025 = [
  '2025-01-01', // 元旦
  '2025-01-28',
  '2025-01-29',
  '2025-01-30',
  '2025-01-31',
  '2025-02-01',
  '2025-02-02',
  '2025-02-03',
  '2025-02-04', // 春节（含调休）
  '2025-04-04',
  '2025-04-05',
  '2025-04-06', // 清明节
  '2025-05-01',
  '2025-05-02',
  '2025-05-03',
  '2025-05-04',
  '2025-05-05', // 劳动节
  '2025-05-31',
  '2025-06-01',
  '2025-06-02', // 端午节
  '2025-10-01',
  '2025-10-02',
  '2025-10-03',
  '2025-10-04',
  '2025-10-05',
  '2025-10-06',
  '2025-10-07',
  '2025-10-08', // 国庆节+中秋节
];

export const weekdayShouldWorkOf2025 = ['2025-01-26', '2025-02-08', '2025-04-27', '2025-09-28', '2025-10-11'];

export const calculateDayDiffResult = (
  startTimestamp: number,
  endTimestamp: number,
  holidays: string[] = [...holidayOf2024, ...holidayOf2025], // 格式：['2025-01-01', '2025-05-01']
) => {
  let current = dayjs.unix(startTimestamp);
  let end = dayjs.unix(endTimestamp);

  // 交换顺序保证正向遍历
  if (current.isAfter(end)) {
    [current, end] = [end, current];
  }

  let workdays = 0;
  const totalDays = Math.abs(end.diff(dayjs.unix(startTimestamp), 'day'));
  while (totalDays > 0 && (current.isBefore(end) || current.isSame(end))) {
    const currentFormat = current.format('YYYY-MM-DD');
    let isWeekend = current.day() === 0 || current.day() === 6; // 0=周日 6=周六
    if (weekdayShouldWorkOf2024.includes(currentFormat) || weekdayShouldWorkOf2025.includes(currentFormat)) {
      isWeekend = false;
    }
    const isHoliday = holidays.includes(currentFormat);

    if (!isWeekend && !isHoliday) {
      workdays++;
    }
    current = current.add(1, 'day');
  }

  return {
    totalDays,
    workdays,
  };
};

export const convertLibraNewInfoToBaseInfo = (libraNewInfo: LibraNewInfo) => {
  const appId = getAppIdByLibraAppId(libraNewInfo.libraAppId);
  const appName = AppId2Name[appId] ?? '';
  const appIds: string[] = [appId];
  const appNames: string[] = [appName];
  const flightId = libraNewInfo.flightInfo.id.toString();
  const flightName = libraNewInfo.flightInfo.name;
  const flightUrl = libraDetailUrl(libraNewInfo.flightInfo.region, libraNewInfo.flightInfo.id);
  const flightOwners: User[] = libraNewInfo.flightInfo.owners;
  const flightType = flightTypeDisplayName(libraNewInfo.flightInfo.libraType);
  const operator = libraNewInfo.flightInfo.closeAttributionInfo?.updateUser?.replace('@bytedance.com', '') ?? ''; // 暂时只记录关闭的情况
  return { appIds, appNames, flightId, flightName, flightUrl, flightOwners, flightType, operator } as LibraBaseInfo;
};
