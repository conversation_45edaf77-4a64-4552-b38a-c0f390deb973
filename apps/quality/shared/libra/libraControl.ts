import { Flight, LastGradualTraffic } from '@shared/libra/flight';
import dayjs from 'dayjs';
import { LibraVersionPlatform } from '@shared/libra/libraInfo';
import utc from 'dayjs/plugin/utc';
import { MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import { MeegoInfo } from '@shared/libra/LibraCreate';
import { User } from '@pa/shared/dist/src/core';
import { LibraRegion } from '@shared/libra/commonLibra';
import { LibraFlightType } from '@shared/libra/LibraNewInfo';
import { IndicatorType } from '@shared/libra/common';

export enum LibraControlTakeOverType {
  BasicInfoCheckFailed = 'basic_info_check_failed', // 实验基础信息检查（实验基础信息 + 实验设计文档）
  BugNotSolved = 'bug_not_solved', // Bug 未解决（P0/P1 问题未处理）
  BugNotSolvedForPatrol = 'bug_not_solved_for_patrol', // 有未解决的P0业务巡检问题
  TimeOrTrafficNotSatisfied = 'time_or_traffic_not_satisfied', // 放量时间间隔过短、或流量大于当前三倍，不满足要求
  StartReleaseWithoutGray = 'start_release_without_gray', // 实验未开启过灰度，不允许开正式
  GrayNotAllowedAfter3rdGray = 'gray_not_allowed_after_3rd_gray', // 跟版需求三灰后禁止开启灰度实验
  CommercialLibraCubaCheckFailed = 'commercial_libra_cuba_check_failed', // 商业化实验，CUBA 检查失败
  CommercialLibraCheckFailed = 'commercial_libra_check_failed', // 商业化实验检查失败
  PCLibraCheckFailed = 'pc_check_failed', // PC 检查失败
  Other = 'other', // 其他
}

export interface LibraControlReq {
  app_id: string; // 产品Id
  flight_id: string; // 实验id
  user: string; // 操作人
  event_type: FlightEventType; // 执行类型
  version_resource: number; // 实验流量
  region: string; // 实验在哪个控制面  枚举值: cn, sg,  va
  owners: string[]; // 实验owner
  tags: string[]; // 实验tags
  configs: any[]; // 实验组配置
  priority: number; // 实验层优先级
  flight?: Flight; // 实验详细信息(接口填充)
}

export interface LibraControlRes {
  take_over: boolean; // 是否被接管
  take_over_reason: string; // 接管理由
  take_over_logid?: string; // 管控记录logid
  take_over_type?: LibraControlTakeOverType; // 接管类型
}

export interface LibraControlRecord extends LibraControlReq, LibraControlRes {
  _id: string;
  flight_name?: string; // 实验名称
  create_ts: number; // 工单时间
  status: ControlStatus;
  chat_id?: string;
  process_history: ControlProcess[];
  last_gradual_traffic?: LastGradualTraffic; // 平滑放量信息
}

export interface LibraControlRecordExt extends LibraControlRecord {
  is_small_flow?: boolean; // 是否小流量发布
  in_peak_time?: boolean; // 是否在高峰期
  version?: string; // 版本
  platform?: string; // 平台
  actual_ts?: number; // 实际操作的时间
}

export interface ApprovalInfo {
  approve_user: string;
  approve_ts: number;
  approve_reason: string;
}

export interface ControlProcess {
  process_type: ControlProcessType;
  success: boolean;
  reason: string;
  ts: number;
  logid: string;
  extra?: any;
}

export enum ControlProcessType {
  Retry = '重试',
  Approval = '审批',
  Operate = '操作实验',
  Cancel = '取消实验操作',
}

export enum PaControlType {
  AfterThridGray = 'AfterThirdGray', // 跟版需求三灰后开启灰度实验
  AfterFullRelease = 'AfterFullRelease', // 版本全量后未开启正式实验
  FreeTest100 = 'FreeTest100', // 跟版免测需求未在集成前开启 100% 流量灰度实验
  GrayFlightDidNot100 = 'GrayFlightDidNot100', // 跟版需求未在灰度阶段开启 100% 灰度实验 - 卡口（阻塞当前灰度节点准出）
}

export enum ExemptStatus {
  Block = 0,
  Exempt = 1,
  Assessment = 2,
}

export interface PaControlRecord {
  app_id: number[];
  libra_app_id: number[];
  flight_id?: number;
  flight_name: string;
  control_type: PaControlType;
  owner_email: string;
  influence: string;
  exempt_reason: string;
  improve_measure: string;
  status: ControlStatus;
  meego_info: MeegoInfo;
  chat_id?: string;
  process_history?: ControlProcess[];
  approval_info?: ApprovalInfo;
  extra?: any;
  exempt_status: ExemptStatus;
  create_ts?: number;
  version: string[];
}

/**
 * 1. 接管 -> 通过
 * 2. 接管 -> 拦截 -> 审批 -> 通过
 */
export enum ControlStatus {
  TakeOver = 'TakeOver', // 接管（被拦截）
  Pass = 'Pass', // 通过
  Approve = 'Approve', // 审批通过
  Cancel = 'Cancel', // 取消管控
}

export enum FlightEventType {
  StartFlight = 'start_flight',
  ChangeVersionResource = 'change_version_resource',
  SuspendFlight = 'suspend_flight',
  ContinueFlight = 'continue_flight',
  StopFlight = 'stop_flight',
  PreprocessStopFlight = 'preprocess_stop_flight', // 历史存量未接管实验自动关停会用到
}

export function getFlightEventName(record: LibraControlRecord) {
  switch (record.event_type) {
    case FlightEventType.StartFlight:
      return `开启实验(流量${record.version_resource * 100}%)`;
    case FlightEventType.ChangeVersionResource:
      return `实验放量(流量${record.version_resource * 100}%)`;
    case FlightEventType.StopFlight:
      return `关闭实验`;
    default:
      return '';
  }
}

export function formatLibraTime(ts: number) {
  dayjs.extend(utc);
  return dayjs.unix(ts).utcOffset(8).format('YYYY-MM-DD HH:mm:ss');
}

export function buildRecordUrl(record: LibraControlRecord, isCN = true) {
  return `${MAIN_HOST_HTTPS}/libra/control/record?id=${record._id}&appid=${isCN ? '177502' : '300602'}`;
}

export function isBetaFlight(p?: LibraVersionPlatform) {
  if (p?.android?.isHit && p.android.isBeta) {
    return true;
  }
  if (p?.iphone?.isHit && p.iphone.isBeta) {
    return true;
  }
  return false;
}

export enum AbnormalFlightReportInfoType {
  StoryFlightNotStartBeforeMR = 0, // 跟版需求未在合码前创建并开启实验
  StoryOfNoTestingNotStartFullTrafficGrayFlight = 1, // 跟版免测需求未在集成测试开启 100% 灰度实验
  StoryStartFlightAfterThirdGray = 2, // 跟版需求三灰后开启灰度实验
  StoryReleaseFlightWithNoGrayFlight = 3, // 未开过灰度实验直接开正式实验
  StoryNotCloseGrayFlightBeforSubmitForReview = 4, // 提审前未关闭灰度实验
  StoryNotStartReleaseFlightAfterFullRelease = 5, // 版本全量后未开启正式实验
  StoryOverdueFlightAfterFullRelease = 6, // 版本全量后 30 天仍在进行中的实验
  StoryFlightDailyInspectMetricDeterioration = 7, // 跟版实验巡检指标劣化（灰度实验+正式实验）
  StoryOfNotFullTrafficGrayFlight = 8, // 跟版需求未在灰度阶段开启 100% 灰度实验
}

export interface MetricDeterioration {
  name: string;
  value: number;
  indicatorType: IndicatorType;
}

export interface AbnormalFlightReportInfo {
  meegoId: number;
  meegoName: string;
  meegoPMOwners?: User[];
  meegoTechOwners?: User[];
  meegoPrimaryBusiness: string; // 一级业务线
  meegoSecondaryBusiness: string; // 二级业务线
  meegoPublishedApps: string[]; // 上线应用
  meegoType: string; // 需求类型（产品需求、技术需求等）
  meegoSourceBusiness: string; // 需求来源业务线
  flightId: number;
  flightAbnormalType: AbnormalFlightReportInfoType;
  flightName: string;
  flightOwners: User[];
  flightStartTime: number;
  flightEndTime: number;
  flightType: LibraFlightType; // 实验类型
  flightRegion: LibraRegion; // 实验地区（sg、va、cn）
  reportTime: number;
  metricDeteriorations?: MetricDeterioration[]; // 劣化指标
  releaseVersion?: string[];
  version?: string;
  appId?: number;
}
