import { FilterOptionType, FilterType } from '@shared/utils/conditionFilter';
import { User } from '@pa/shared/dist/src/core';
import { LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import { LibraFlightStatus, LibraTimeQueryType } from '@shared/libra/LibraNewInfo';

export interface LibraNewInfoTableCustomStatus {
  [key: string]: {
    originEnable: boolean;
    selectedEnable: boolean;
  };
}

export interface LibraInfoColumnFilterConfig {
  filter_type: FilterType;
  key_path: string[];
  value_type: FilterOptionType;
  raw_array?: boolean;
  guide_placeholder?: string;
  filter_name: string;
  filter_id: string;
}

export interface LibraNewInfoTableColumnConfig {
  column_name: string;
  column_id: string;
  lock_enable: boolean;
  default_enable: boolean;
  enable: boolean;
}

export interface LibraNewInfoTableColumnGroup {
  group_name: string;
  group_id: string;
  configs: LibraNewInfoTableColumnConfig[];
}

export interface LibraNewInfoTableColumnSetting {
  groups: LibraNewInfoTableColumnGroup[];
  user_email: string;
}

export const LibraInfoColumnFilterConfigs = (): LibraInfoColumnFilterConfig[] => [
  {
    filter_id: 'name_search',
    filter_type: FilterType.FilterTypeText,
    key_path: ['flightInfo.name', 'flightInfo.description'],
    value_type: FilterOptionType.TypeString,
    filter_name: '名称/描述',
    guide_placeholder: '请输入实验名称/描述',
  },
  {
    filter_id: 'name_meego',
    filter_type: FilterType.FilterTypeText,
    key_path: ['meegoInfo.name'],
    value_type: FilterOptionType.TypeString,
    filter_name: '需求名称',
    guide_placeholder: '请输入需求名称',
  },
  {
    filter_id: 'version_search',
    filter_type: FilterType.FilterTypeText,
    key_path: ['meegoInfo.releaseVersion'],
    value_type: FilterOptionType.TypeString,
    filter_name: '版本',
    guide_placeholder: '请输入实验版本',
  },
  {
    filter_id: 'tag_search',
    filter_type: FilterType.FilterTypeText,
    key_path: ['flightInfo.tags'],
    value_type: FilterOptionType.TypeString,
    filter_name: '实验标签',
    guide_placeholder: '请输入实验标签',
  },
  {
    filter_id: 'id_search',
    filter_type: FilterType.FilterTypeText,
    key_path: ['flightInfo.id', 'flightInfo.versions.vid'],
    value_type: FilterOptionType.TypeNumber,
    filter_name: 'ID/实验组ID',
    guide_placeholder: '请输入实验ID/实验组ID',
  },
  {
    filter_id: 'test_user_search',
    filter_type: FilterType.FilterTypeText,
    key_path: ['flightInfo.versions.testUser'],
    value_type: FilterOptionType.TypeString,
    filter_name: '测试用户',
    guide_placeholder: '请输入测试用户',
  },
];

export const LibraInfoColumnDefaultSetting = (): LibraNewInfoTableColumnSetting => ({
  user_email: '',
  groups: [
    {
      group_name: '实验信息',
      group_id: 'flight_info',
      configs: [
        {
          column_name: '实验名称/描述/业务线/标签',
          column_id: 'flight_name',
          lock_enable: true,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验ID',
          column_id: 'flight_id',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验组',
          column_id: 'flight_versions',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '时间',
          column_id: 'flight_start_time',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验类型',
          column_id: 'flight_type',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验状态',
          column_id: 'flight_status',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验Owner',
          column_id: 'flight_owner',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '流量信息',
          column_id: 'flight_traffic_info',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '分流类型',
          column_id: 'flight_layer_type',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验标签',
          column_id: 'flight_tags',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '进组人数',
          column_id: 'flight_user_count',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '实验关闭归因',
          column_id: 'flight_close_attribution',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: 'Libra 关闭原因(仅参考)',
          column_id: 'flight_close_attribution_from_libra',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '质量问题实验',
          column_id: 'flight_quality_problem',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        // {
        //   column_name: '重开归因',
        //   column_id: 'flight_reopen_type',
        //   lock_enable: false,
        //   default_enable: true,
        //   enable: false,
        // },
        // {
        //   column_name: '重开详细原因',
        //   column_id: 'flight_reopen_detail',
        //   lock_enable: false,
        //   default_enable: true,
        //   enable: false,
        // },
        // {
        //   column_name: '关闭原因',
        //   column_id: 'flight_stop_type',
        //   lock_enable: false,
        //   default_enable: true,
        //   enable: true,
        // },
        // {
        //   column_name: '关闭详细归因',
        //   column_id: 'flight_stop_detail',
        //   lock_enable: false,
        //   default_enable: true,
        //   enable: true,
        // },
        // {
        //   column_name: '关闭备注',
        //   column_id: 'flight_stop_remark',
        //   lock_enable: false,
        //   default_enable: true,
        //   enable: true,
        // },
      ],
    },
    {
      group_id: 'feature_info',
      group_name: '需求信息',
      configs: [
        {
          column_name: '需求名称',
          column_id: 'feature_name',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '业务线',
          column_id: 'business_line',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '业务团队',
          column_id: 'business_team',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '主责业务团队',
          column_id: 'main_business_team',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '上线应用',
          column_id: 'published_app',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '跟车版本',
          column_id: 'published_version',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '操作',
          column_id: 'operations',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
      ],
    },
    {
      group_id: 'patrol_info',
      group_name: '巡检管控信息',
      configs: [
        {
          column_name: '稳定性指标',
          column_id: 'stability_metrix',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '业务核心指标',
          column_id: 'business_metrix',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '反馈指标',
          column_id: 'feedback_metrix',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
      ],
    },
  ],
});

export const LibraNewInfoColumnIdSort = (): string[] => [
  'flight_id',
  'flight_name',
  'flight_status',
  'flight_versions',
  'flight_traffic_info',
  'flight_owner',
  'flight_start_time',
  'flight_type',
  'flight_layer_type',
  'flight_tags',
  'flight_user_count',
  'flight_close_attribution',
  'flight_close_attribution_from_libra',
  'flight_quality_problem',
  // 'flight_reopen_type',
  // 'flight_reopen_detail',
  // 'flight_stop_type',
  // 'flight_stop_detail',
  // 'flight_stop_remark',
  'feature_name',
  'business_line',
  'business_team',
  'main_business_team',
  'published_app',
  'published_version',
  'stability_metrix',
  'business_metrix',
  'feedback_metrix',
  'operations',
];

export interface LibraNewInfoTablePageStatus {
  column_groups?: LibraNewInfoTableColumnGroup[];
  sort_config?: any;
  find_config?: any;
  filter_config?: {
    filter_id?: string;
    filter_value?: string;
  };
  onlyMineChecked?: boolean;
  selectedOwner?: User[];
  selectedAppIds?: string[];
  selectedBusinessLine?: string[];
  selectedBusinessTeam?: string[];
  selectedFlightStatus?: LibraFlightStatus[];
  selectedFlightStartTime?: number;
  selectedFlightEndTime?: number;
  selectedFlightTimeType?: LibraTimeQueryType;
  selectedCloseAttribution?: string[];
  userRoleType?: LibraPlatformRoleType;
}
