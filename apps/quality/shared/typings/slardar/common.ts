import commonUtils from '@shared/utils/commonUtils';
import { CrashType, GetCrashListRequest } from '@shared/typings/slardar/crash/issueListSearch';
import { CommonOf } from '@shared/utils/tools';
import { GetCrashTrendRequest } from '@shared/typings/slardar/crash/trend';
import { MAIN_HOST_HTTPS, SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { DBAlarmVersion } from '../../../api/model/AlarmVersionInfoTable';
import {
  DeviceLevel,
  highAndroidCondition,
  highIosCondition,
  lowAndroidCondition,
  lowIosCondition,
} from '@shared/common';

export const ANDROID_CHANEL_NOT_IN = [
  'local_test',
  'release',
  'auto_test',
  'asan',
  'auto_test_byteinsight',
  'auto_test_asan',
  'auto_test_fission',
  'byte_test',
  'mr_auto_test',
  'mr_auto_test_bi',
  'auto_test_dev',
  'debug',
  'auto_test_for_mr',
  'auto_test_outer',
  '',
];

export const ANDROID_OFFLINE_CHANNEL_NOT_IN: Readonly<{
  [aid: number]: string[];
}> = {
  1775: [
    'update',
    'honor_64_1775',
    'huawei_64_1775',
    'vivo_64_1775',
    'oppo_64_1775',
    'xiaomi_64_1775',
    'tengxun',
    'xiaomi',
    'huawei',
    'vivo',
    'local_test',
    '',
  ],
  3006: ['googleplay', 'local_test', ''],
};

export const IOS_OFFLINE_CHANNEL_IN = [
  'inhouse',
  'auto_test',
  'auto_test_byteinsight',
  'auto_test_fission',
  'byte_test',
  'mr_auto_test',
  'mr_auto_test_bi',
  'auto_test_dev',
  'auto_test_looki',
  'auto_test_ttp',
  'auto_test_outer',
  'local_test',
  'monkey',
  '',
];

export type GranularityType = '604800' | '86400' | '43200' | '21600' | '3600' | '1800' | '600' | '300' | '60';

export type SlardarCrashCommon = CommonOf<GetCrashTrendRequest, GetCrashListRequest>;

export const CrashType2Url: Readonly<{
  [key: string]: string;
}> = {
  JavaStartCrash: 'start',
  JavaCrash: 'app',
  NativeCrash: 'native',
  ANR: 'anr',
  JavaOOM: 'app',
  NativeOOM: 'native',
  NativeMemLeak: 'native_exception',
  Lag: 'lag',
  SeriousLag: 'serious_lag',
  NativeJava: 'native_java',

  // iOS
  OOMCrash: 'oom_crash',
  Crash: 'crash',
  WatchDog: 'watch_dog',
  custom_exception: 'custom_exception',
  CpuMeTriket: 'ios_metricket_cpu',
};

export const crashType2MeasureName: Readonly<{
  [key: string]: string;
}> = {
  start: 'Java启动崩溃',
  app: 'Java崩溃',
  native: 'Native崩溃',
  native_start: 'Native启动崩溃',
  anr: 'ANR',
  activity: 'Java内存泄漏',
  native_exception: 'Native内存泄漏',
  lag: '卡顿',
  serious_lag: '严重卡顿',

  // iOS
  crash: '崩溃',
  oom_crash: 'OOM崩溃',
  watch_dog: '卡死',
  custom_exception: '自定义异常',
};

export const crashType2Title: Readonly<{
  [key: string]: string;
}> = {
  start: 'Java 启动崩溃',
  app: 'Java 崩溃',
  native: 'Native 崩溃',
  anr: 'ANR',
  activity: 'Java 内存泄漏',
  instance: 'Java 小对象',
  native_exception: 'Native 内存泄漏',
  lag: '卡顿',
  serious_lag: '严重卡顿',

  // iOS
  crash: 'Crash',
  oom_crash: 'OOM',
  watch_dog: '卡死',
  custom_exception: '自定义异常',
  ios_metricket_cpu: 'CPU异常',
};

export function getSlardarUrl(aid: number, crashType: string, platform: string, location: string, params?: string) {
  const oversea = [3006, 7356].includes(aid);
  const baseParam: Record<string, any> = {
    aid,
    os: platform,
    region: !oversea ? 'cn' : 'maliva',
    lang: 'zh-Hans',
  };
  if (oversea) {
    baseParam.subregion = 'row';
  }
  let method: string;
  if (location.includes('activity') && oversea) {
    method = 'https://slardar-sg.tiktok-row.net/node/app_detail/';
  } else {
    method = !oversea
      ? 'https://slardar.bytedance.net/node/app_detail/'
      : 'https://slardar-sg.tiktok-row.net/node/app_detail/';
  }
  let url = `${method}?${commonUtils.getAsUriParameters(baseParam)}`;
  if (['abnormal_list', 'perf_v2/smooth_v2'].includes(location)) {
    url = `${url}#/${location}/${crashType}`;
  } else {
    url = `${url}#/${location}`;
  }
  if (params) {
    url = `${url}?${params}`;
  }
  return url;
}

export function getSlardarLagParams(start_time: number, end_time: number, versionCode: string, granularity = 3600) {
  return commonUtils.getAsUriParameters({
    params: JSON.stringify({
      start_time,
      end_time,
      granularity,
      filters_conditions: {
        type: 'and',
        sub_conditions: [{ dimension: 'update_version_code', op: 'in', values: [versionCode] }],
      },
      min_sample_count: 1000,
    }).toString(),
  });
}

function getNextHourTimestamp(timestamp: number) {
  // 将时间戳转换为Date对象
  const date = new Date(timestamp * 1000); // 因为JavaScript的时间戳单位是毫秒
  if (date.getMinutes() === 0 && date.getSeconds() === 0 && date.getMilliseconds() === 0) {
    // 如果当前已经是整点，不需要加1
  } else {
    // 设置分钟和秒为0，毫秒为0
    date.setMinutes(0, 0, 0);
    date.setHours(date.getHours() + 1);
  }
  // 将Date对象转换回时间戳
  return Math.floor(date.getTime() / 1000); // 转换为秒
}

export async function getReasonUrl(
  bitsAppid: number,
  crashType: string,
  baseVersion: DBAlarmVersion,
  targetVersion: DBAlarmVersion,
  baseStartTime: number,
  targetStartTime: number,
) {
  const baseReleaseTime = baseVersion.timestamp;
  const targetReleaseTime = targetVersion.timestamp;
  baseStartTime = getNextHourTimestamp(baseStartTime);
  targetStartTime = getNextHourTimestamp(targetStartTime);

  let maxHour = 3;
  const now = Math.floor(Date.now() / 1000);
  const diffHour = (now - targetStartTime) / 3600;
  if (diffHour > 24) {
    maxHour = 24;
  } else if (diffHour > 12) {
    maxHour = 12;
  }

  const params = {
    appid: bitsAppid,
    version: targetVersion.version,
    tab: 'version',
    crash_type: crashType,
    base_version_code: baseVersion.version,
    target_version_code: targetVersion.version,
    base_update_version_code: baseVersion.version_code,
    target_update_version_code: targetVersion.version_code,
    base_release_time: baseReleaseTime,
    target_release_time: targetReleaseTime,
    base_start_time: baseStartTime,
    target_start_time: targetStartTime,
    base_end_time: baseStartTime + maxHour * 3600,
    target_end_time: targetStartTime + maxHour * 3600,
  };

  const path = 'quality/diagnosis/issue-attribution/multi-attribution';
  const url = new URL(`${MAIN_HOST_HTTPS}/${path}`);
  for (const [key, value] of Object.entries(params)) {
    url.searchParams.append(key, `${value}`);
  }
  return url.toString();
}

export function getSlardarIssueParams(
  start_time: number,
  end_time: number,
  versionCode: string,
  crashType: string,
  os: 'Android' | 'iOS',
  isCN: boolean,
  isOOM: boolean,
  crash_type?: CrashType,
  version?: string,
  issue_id?: string[],
  granularity = 3600,
  isNew = false,
  deviceLevel: DeviceLevel = DeviceLevel.ALL,
) {
  const defaultParam: Record<string, any> = {
    start_time,
    end_time,
    order_by: 'user_descend',
    pgno: 1,
    pgsz: 10,
    crash_time_type: 'insert_time',
    shortCutKey: 'custom',
    anls_dim: [],
    ios_issue_id_version: 'v2',
    filters_conditions: {
      sub_conditions: [],
      type: 'and',
    },
    versions_conditions: {},
    granularity,
    trend_types: ['count', 'count_start', 'active', 'user_active'],
    crash_type: crashType,
    enableMeego: true,
  };
  if (deviceLevel === DeviceLevel.LOW) {
    defaultParam.filters_conditions.sub_conditions?.push(os === 'iOS' ? lowIosCondition : lowAndroidCondition);
  }
  if (deviceLevel === DeviceLevel.HIGH) {
    defaultParam.filters_conditions.sub_conditions?.push(os === 'iOS' ? highIosCondition : highAndroidCondition);
  }
  if (version) {
    defaultParam.filters_conditions.sub_conditions?.push({
      dimension: 'app_version',
      op: 'in',
      values: [version],
      groupKey: undefined,
      type: 'expression',
    });
  } else {
    defaultParam.filters_conditions.sub_conditions?.push({
      dimension:
        os === 'iOS' // && crash_type === "ios_metric_kit_crash"
          ? 'raw_update_version_code'
          : 'update_version_code',
      op: 'in',
      values: [versionCode],
      type: 'expression',
    });
  }
  if (isOOM) {
    if (crashType === 'native') {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'filters_map',
        groupKey: 'NPTH Tag',
        op: 'in',
        values: ['true'],
        map_key: 'memory_leak',
        type: 'map',
      });
    } else {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'is_oom',
        op: 'eq',
        value: '1',
        type: 'expression',
      });
    }
  }
  if (crash_type === CrashType.NativeMemLeak) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'filters_map',
      op: 'in',
      values: ['true'],
      groupKey: 'custom tag',
      map_key: 'has_native_oom',
      type: 'map',
    });
  }
  if (os === 'Android' && crash_type === CrashType.JavaSmallInstance) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'heap_size',
      op: 'gte',
      value: '200',
      type: 'expression',
    });
  }
  if (os === 'Android' && crash_type && [CrashType.JavaSmallInstance, CrashType.JavaMemLeak].includes(crash_type)) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      type: 'expression',
      op: 'in',
      values: ['instance'],
      dimension: 'memory_object_node_type',
    });
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'memory_object_total_type',
      op: 'in',
      values: ['oom'],
      type: 'expression',
    });
  }
  if (
    os === 'Android' &&
    (!crash_type || (crash_type && ![CrashType.NativeMemLeak, CrashType.JavaMemLeak].includes(crash_type)))
  ) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'channel',
      op: 'not_in',
      type: 'expression',
      values: ANDROID_CHANEL_NOT_IN,
    });
  }
  if (
    os === 'Android' &&
    isCN &&
    (!crash_type || (crash_type && ![CrashType.NativeMemLeak, CrashType.JavaMemLeak].includes(crash_type)))
  ) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'filters_map',
      groupKey: 'custom tag',
      map_key: 'is_bytest_privacy_rom',
      op: 'not_in',
      type: 'map',
      values: ['true'],
    });
  }
  if (issue_id) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'issue_id',
      op: 'in',
      type: 'expression',
      values: issue_id,
    });
  }
  if (isNew) {
    defaultParam.is_new = true;
  }
  return commonUtils.getAsUriParameters({
    params: JSON.stringify(defaultParam).toString(),
  });
}

export function getOfflineSlardarIssueParams(
  aid: number,
  start_time: number,
  end_time: number,
  versionCode: string,
  crashType: string,
  os: 'Android' | 'iOS',
  isCN: boolean,
  isOOM: boolean,
  crash_type?: CrashType,
  version?: string,
  issue_id?: string[],
  granularity = 3600,
  isNew = false,
  deviceLevel: DeviceLevel = DeviceLevel.ALL,
) {
  const defaultParam: Record<string, any> = {
    start_time,
    end_time,
    order_by: 'user_descend',
    pgno: 1,
    pgsz: 10,
    crash_time_type: 'insert_time',
    shortCutKey: 'custom',
    anls_dim: [],
    ios_issue_id_version: 'v2',
    filters_conditions: {
      sub_conditions: [],
      type: 'and',
    },
    versions_conditions: {},
    granularity,
    trend_types: ['count', 'count_start', 'active', 'user_active'],
    crash_type: crashType,
    enableMeego: true,
  };
  if (deviceLevel === DeviceLevel.LOW) {
    defaultParam.filters_conditions.sub_conditions?.push(os === 'iOS' ? lowIosCondition : lowAndroidCondition);
  }
  if (deviceLevel === DeviceLevel.HIGH) {
    defaultParam.filters_conditions.sub_conditions?.push(os === 'iOS' ? highIosCondition : highAndroidCondition);
  }
  if (version) {
    defaultParam.filters_conditions.sub_conditions?.push({
      dimension: 'app_version',
      op: 'in',
      values: [version],
      groupKey: undefined,
      type: 'expression',
    });
  } else {
    defaultParam.filters_conditions.sub_conditions?.push({
      dimension:
        os === 'iOS' // && crash_type === "ios_metric_kit_crash"
          ? 'raw_update_version_code'
          : 'update_version_code',
      op: 'in',
      values: [versionCode],
      type: 'expression',
    });
  }
  if (isOOM) {
    if (crashType === 'native') {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'filters_map',
        groupKey: 'NPTH Tag',
        op: 'in',
        values: ['true'],
        map_key: 'memory_leak',
        type: 'map',
      });
    } else {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'is_oom',
        op: 'eq',
        value: '1',
        type: 'expression',
      });
    }
  }
  if (crash_type === CrashType.NativeMemLeak) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'filters_map',
      op: 'in',
      values: ['true'],
      groupKey: 'custom tag',
      map_key: 'has_native_oom',
      type: 'map',
    });
  }
  if (os === 'Android' && crash_type === CrashType.JavaSmallInstance) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'heap_size',
      op: 'gte',
      value: '200',
      type: 'expression',
    });
  }
  if (os === 'Android' && crash_type && [CrashType.JavaSmallInstance, CrashType.JavaMemLeak].includes(crash_type)) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      type: 'expression',
      op: 'in',
      values: ['instance'],
      dimension: 'memory_object_node_type',
    });
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'memory_object_total_type',
      op: 'in',
      values: ['oom'],
      type: 'expression',
    });
  }
  if (os === 'Android' && (!crash_type || (crash_type && ![CrashType.NativeMemLeak].includes(crash_type)))) {
    if (aid === 1775) {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'channel',
        op: 'not_in',
        type: 'expression',
        values: ANDROID_OFFLINE_CHANNEL_NOT_IN[aid],
      });
    } else if (aid === 3006) {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'channel',
        op: 'not_in',
        type: 'expression',
        values: ANDROID_OFFLINE_CHANNEL_NOT_IN[aid],
      });
    }
  }
  if (os === 'iOS') {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'channel',
      op: 'in',
      type: 'expression',
      values: IOS_OFFLINE_CHANNEL_IN,
    });
  }
  if (os === 'Android' && isCN && (!crash_type || (crash_type && ![CrashType.NativeMemLeak].includes(crash_type)))) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'filters_map',
      groupKey: 'custom tag',
      map_key: 'is_bytest_privacy_rom',
      op: 'not_in',
      type: 'map',
      values: ['true'],
    });
  }
  if (issue_id) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'issue_id',
      op: 'in',
      type: 'expression',
      values: issue_id,
    });
  }
  if (isNew) {
    defaultParam.is_new = true;
  }
  return commonUtils.getAsUriParameters({
    params: JSON.stringify(defaultParam).toString(),
  });
}

export function getSlardarIssueListParams(
  platform: SlardarPlatformType,
  version_code?: string,
  start_time?: number,
  end_time?: number,
  version?: string,
  crashType?: CrashType,
) {
  const isOOM = crashType && [CrashType.JavaOOM, CrashType.NativeOOM, CrashType.OOMCrash].includes(crashType);
  const defaultParam: Record<string, any> = {
    token: '',
    token_type: 0,
    crash_time_type: 'insert_time',
    start_time,
    end_time,
    granularity: 86400,
    filters_conditions: {
      type: 'and',
      sub_conditions: [],
    },
    event_index: 1,
  };
  if (version_code) {
    defaultParam.filters_conditions.sub_conditions.push({
      dimension: version
        ? 'app_version'
        : platform === SlardarPlatformType.Android
          ? 'update_version_code'
          : 'raw_update_version_code',
      op: 'in',
      values: [version ? version : version_code],
      groupKey: null,
      type: 'expression',
    });
  }
  if (crashType === CrashType.NativeMemLeak) {
    defaultParam['filters_conditions'].sub_conditions.push({
      dimension: 'filters_map',
      op: 'in',
      values: ['true'],
      groupKey: 'custom tag',
      map_key: 'has_native_oom',
      type: 'map',
    });
  }
  if (isOOM) {
    if ([CrashType.NativeOOM].includes(crashType)) {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'filters_map',
        groupKey: 'NPTH Tag',
        op: 'in',
        values: ['true'],
        map_key: 'memory_leak',
        type: 'map',
      });
    } else {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'is_oom',
        op: 'eq',
        value: '1',
        type: 'expression',
      });
    }
  }
  if (
    platform === SlardarPlatformType.Android &&
    (!crashType || (crashType && ![CrashType.NativeMemLeak, CrashType.JavaMemLeak].includes(crashType)))
  ) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'channel',
      op: 'not_in',
      type: 'expression',
      values: ANDROID_CHANEL_NOT_IN,
    });
  }
  return commonUtils.getAsUriParameters({
    params: JSON.stringify(defaultParam).toString(),
  });
}

export function getOfflineSlardarIssueListParams(
  platform: SlardarPlatformType,
  aid: number,
  version_code?: string,
  start_time?: number,
  end_time?: number,
  version?: string,
  crashType?: CrashType,
) {
  const isOOM = crashType && [CrashType.JavaOOM, CrashType.NativeOOM, CrashType.OOMCrash].includes(crashType);
  const defaultParam: Record<string, any> = {
    token: '',
    token_type: 0,
    crash_time_type: 'insert_time',
    start_time,
    end_time,
    granularity: 86400,
    filters_conditions: {
      type: 'and',
      sub_conditions: [],
    },
    event_index: 1,
  };
  if (version) {
    defaultParam.filters_conditions.sub_conditions.push({
      dimension: version
        ? 'app_version'
        : platform === SlardarPlatformType.Android
          ? 'update_version_code'
          : 'raw_update_version_code',
      op: 'in',
      values: [version ? version : version_code],
      groupKey: null,
      type: 'expression',
    });
  }
  if (crashType === CrashType.NativeMemLeak) {
    defaultParam['filters_conditions'].sub_conditions.push({
      dimension: 'filters_map',
      op: 'in',
      values: ['true'],
      groupKey: 'custom tag',
      map_key: 'has_native_oom',
      type: 'map',
    });
  }
  if (isOOM) {
    if ([CrashType.NativeOOM].includes(crashType)) {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'filters_map',
        groupKey: 'NPTH Tag',
        op: 'in',
        values: ['true'],
        map_key: 'memory_leak',
        type: 'map',
      });
    } else {
      defaultParam.filters_conditions?.sub_conditions?.push({
        dimension: 'is_oom',
        op: 'eq',
        value: '1',
        type: 'expression',
      });
    }
  }
  if (
    platform === SlardarPlatformType.Android &&
    (!crashType || (crashType && ![CrashType.NativeMemLeak, CrashType.JavaMemLeak].includes(crashType)))
  ) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'channel',
      op: 'not_in',
      type: 'expression',
      values: ANDROID_OFFLINE_CHANNEL_NOT_IN[aid],
    });
  }
  if (platform === SlardarPlatformType.iOS) {
    defaultParam.filters_conditions?.sub_conditions?.push({
      dimension: 'channel',
      op: 'in',
      type: 'expression',
      values: IOS_OFFLINE_CHANNEL_IN,
    });
  }
  return commonUtils.getAsUriParameters({
    params: JSON.stringify(defaultParam).toString(),
  });
}

export function AutoTestBugTitle2CrashType(title: string): string | undefined {
  const titleMap: Record<string, string> = {
    'Native崩溃(Java聚类)': `native_java`,
    '发生java 崩溃': 'app',
    发生启动崩溃: 'start',
    '发生Native 崩溃': 'native',
    发生ANR: 'anr',
    发生崩溃: 'crash',
    发生卡死: 'watch_dog',
    发生自定义异常: 'custom_exception',
    发生内存泄露: 'mom',
  };
  for (const key in titleMap) {
    if (title.includes(key)) {
      return titleMap[key];
    }
  }
  return undefined;
}
