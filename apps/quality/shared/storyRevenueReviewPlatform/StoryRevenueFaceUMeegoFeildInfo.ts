export interface StoryRevenueFaceUMeegoFeildType {
  field_name: string;
  field_key: string;
  options?: {
    [key: string]: any;
  }[];
}

export const StoryRevenueFaceUMeegoFeildInfo: StoryRevenueFaceUMeegoFeildType[] = [
  {
    options: [
      {
        label: '视频工具与素材',
        value: '65b78a636c17cc737cede334',
        children: [
          {
            label: '视频工具',
            value: '65b9f6ee05d6a08bc2573443',
          },
          {
            label: '素材工具',
            value: '65b9f6802fb32ede619478fb',
          },
          {
            value: '65b9f6f6b7f49c757876d9ed',
            label: '素材分发',
          },
          {
            label: 'Smart Edit',
            value: '65b9ec96bdb200810812e2fc',
          },
        ],
      },
      {
        label: '醒图Hypic',
        value: '65b78a72fb04e63ddcf27a17',
        children: [
          {
            label: 'AI创新',
            value: '65b9ed0cc04d337f60ee7319',
          },
          {
            value: '65b9eeec7cdb8b48915c2491',
            label: '人像&图片编辑',
          },
          {
            label: '平面排版',
            value: '65b9ed20eba4437e42ce5dfe',
          },
          {
            label: '编辑器主框架和策略',
            value: '65b9ed2f4c8011a004290e77',
          },
          {
            label: '主框架',
            value: '6776762e126944a586bcac3e',
          },
          {
            label: '工具',
            value: '6776764e5ca08fb74bc288f3',
          },
          {
            label: '模板',
            value: '677676536f82faee1f5393a7',
          },
        ],
      },
      {
        label: '内容生态',
        value: '65b78a8abb0b7440ee555b79',
        children: [
          {
            label: '作者',
            value: '65b9ed4805d6a08bc2573440',
          },
          {
            label: '模板框架与分发',
            value: '65b9ed4c19bb0ccfe30bf225',
          },
          {
            label: '模板工具',
            value: '65b9ed555d26d00f591dfec2',
          },
        ],
      },
      {
        label: '主框架&基础产品',
        value: '65b78ab324e6075bc99882e8',
        children: [
          {
            label: '主框架',
            value: '65b9ee5cfe8e8a2ba7d76fe1',
          },
          {
            label: '基础产品',
            value: '65b9ee62c545dcc9935ba741',
          },
          {
            label: '插件化',
            value: '65b9ee68f8a874d45c1ccb20',
          },
        ],
      },
      {
        label: '智能成片',
        value: '65b9ec9f40049dd5998b2d55',
      },
      {
        label: '即梦Dreamina',
        value: '65b78a95e41c13baa2df8b4f',
        children: [
          {
            value: '65b9eca9fe8e8a2ba7d76fe0',
            label: 'AI视频',
          },
          {
            label: 'AI生图',
            value: '65b9ecb04c8011a004290e76',
          },
          {
            label: 'AI主框架',
            value: '662db38f31651ae9817bc539',
          },
          {
            label: '用户社区',
            value: '6646cdeb780993bbc276e131',
          },
          {
            label: '内容平台',
            value: '66558be4dae2b977c61c6cc9',
          },
        ],
      },
      {
        label: '商业化',
        value: '65b78a9fa8e10bb0037ca666',
        children: [
          {
            label: '经营分析',
            value: '65b9eda2a1f0cc1085a24eb6',
          },
          {
            label: '订阅',
            value: '65b9eda733ab56f433a1f023',
          },
          {
            label: '广告',
            value: '65b9edacb7f49c757876d9ec',
          },
        ],
      },
      {
        label: 'UG',
        value: '65560a963e56a6f76f7cea91',
        children: [
          {
            label: '剪映UG',
            value: '6563ffca77eb6f2a21263505',
          },
          {
            label: 'Capcut UG',
            value: '6563ffe1d07a5e787da10841',
          },
          {
            label: 'Dreamina UG',
            value: '6564002deb4ff95df280d671',
          },
          {
            label: '抖T联动',
            value: '669644493d534011a03198b6',
          },
          {
            label: 'SEO增长',
            value: '669644665c5909fdc0d46191',
          },
          {
            label: '社媒增长',
            value: '669644783ca67333fd583e5c',
          },
        ],
      },
      {
        label: 'vimo与素材库',
        value: '65b78aada51f65883399d216',
        children: [
          {
            label: 'vimo后台',
            value: '65b9ee3df8a874d45c1ccb1f',
          },
          {
            label: '版权平台',
            value: '65b9ef1fc6c5db507be6a501',
          },
        ],
      },
      {
        label: '营销工具',
        value: '65b78aa73219a70ff778f2c8',
        children: [
          {
            label: '权益方案',
            value: '65b9edc7a1f0cc1085a24eb7',
          },
          {
            label: '产品核心能力',
            value: '65b9edf6c6c5db507be6a500',
          },
        ],
      },
      {
        label: '安全合规',
        value: '65b78ad457483f37bf3e89a4',
        children: [
          {
            value: '65e00b2fb85ded2fdab32dac',
            label: '中国区-安全合规',
          },
          {
            label: '非中区-安全合规',
            value: '65e00b3a862597d04e0bc55a',
          },
        ],
      },
      {
        label: '多媒体',
        value: '666be9c578aa933062d07ccc',
      },
      {
        label: '剪映',
        value: '6776752fbbf2933296718857',
        children: [
          {
            label: 'UGC与工具增长',
            value: '67767549bbf2933296718858',
          },
          {
            label: 'PGC',
            value: '677675576f82faee1f5393a6',
          },
          {
            label: '编辑器及特效产品',
            value: '67767561f63559f90ed76bba',
          },
          {
            label: '内容生态与分发',
            value: '677675a0f5a1bb6a2e8dc7bf',
          },
          {
            label: 'AI剪辑',
            value: '677675ab364345076aa3f53c',
          },
        ],
      },
      {
        label: 'CapCut',
        value: '6776737ba18fb84661bb8dff',
        children: [
          {
            value: '6776739c77b245481210662c',
            label: 'AI特效开放',
          },
          {
            label: '模板工具与分发',
            value: '677673be5ca08fb74bc288f1',
            children: [
              {
                label: '模板框架与体验',
                value: '6776741e2d9caf4610812327',
              },
              {
                label: '搜推分发',
                value: '6776742df63559f90ed76bb9',
              },
              {
                label: '生态基建',
                value: '677674327fb703229ca2da4b',
              },
            ],
          },
          {
            children: [
              {
                label: '文本编辑器',
                value: '6776743d9f18eb6e50451284',
              },
              {
                label: '多轨AI',
                value: '677674572d9e52de81ec0256',
              },
              {
                label: '基础编辑器',
                value: '6776745d77b245481210662d',
              },
              {
                label: 'CC智能成片',
                value: '677674af2d9caf4610812328',
              },
            ],
            label: '多轨工具',
            value: '677673ce2d9caf4610812326',
          },
          {
            label: '主框架与Web',
            value: '677673f824dc7e8b89bfe79c',
          },
          {
            value: '677673ff7fb703229ca2da4a',
            label: '营销策略',
          },
          {
            label: '内容与作者平台',
            value: '6776740a5ca08fb74bc288f2',
            children: [
              {
                label: '作者策略',
                value: '677675fe9f18eb6e50451285',
              },
              {
                label: '素材生产与分发',
                value: '67767607a55757a8d4b94508',
              },
            ],
          },
        ],
      },
      {
        label: '基础技术',
        value: '65b78ac8eee88dcefd577896',
      },
      {
        label: '服务架构',
        value: '65e00b63191094c74c554474',
      },
      {
        label: 'meego试用业务线',
        value: '65f18c7c989ea5091bd042df',
      },
      // {
      //   label: 'QA空间',
      //   value: '66f3da80c6349c2505c01c80',
      //   children: [
      //     {
      //       label: '对比任务',
      //       value: '66f3de03d57c62fef983e818',
      //     },
      //   ],
      // },
      // {
      //   label: '历史配置',
      //   value: '65b78a4d3c918b8fa4f50762',
      //   children: [
      //     {
      //       label: '【勿选】配置中',
      //       value: '65b78a2df45f2e99e19ecf30',
      //     },
      //     {
      //       label: '剪映',
      //       value: '5cf8e4e44da26751ae4391de',
      //     },
      //     {
      //       label: 'CapCut',
      //       value: '61d53a1e085b1c2110aca4dc',
      //     },
      //     {
      //       label: '剪映专业版',
      //       value: '5fb62e0f626f5c46995dad19',
      //     },
      //     {
      //       value: '60efd5c6d9fc6b0793a517a8',
      //       label: 'CapCut专业版',
      //     },
      //     {
      //       label: '剪映Web',
      //       value: '61d53a4ae1941fa494e17c58',
      //     },
      //     {
      //       label: 'CapCut Web',
      //       value: '637f2a0cbeb618d5538eb58b',
      //     },
      //     {
      //       label: '醒图',
      //       value: '5d24305aca21d325ad6e3d4a',
      //     },
      //     {
      //       label: 'Mweb',
      //       value: '64f0101ff0490a2f131bd1ea',
      //     },
      //     {
      //       label: '黑罐头',
      //       value: '5f64845a91e28cfc0f049338',
      //     },
      //     {
      //       label: 'FaceU',
      //       value: '5d0341b425dd203a42b20b95',
      //     },
      //     {
      //       label: 'Faceubot',
      //       value: '5d7f5a1e16866b01acb0bc23',
      //     },
      //     {
      //       label: '有咔',
      //       value: '5d0341d825dd203a42b3d5a7',
      //     },
      //     {
      //       label: '轻颜',
      //       value: '5d0341c825dd203a42b306c8',
      //     },
      //     {
      //       label: 'CC4B Ad Cube',
      //       value: '62f21b1ba7084c6367e69a04',
      //     },
      //     {
      //       label: 'CapCut电商',
      //       value: '65ae37503d94b7502ec1fc48',
      //     },
      //   ],
      // },
    ],
    field_name: '业务线',
    field_key: 'business',
  },
  {
    field_key: 'field_2a112b',
    field_name: '子模块',
    options: [
      {
        label: '素材',
        value: '46e1845az',
      },
      {
        label: '作者',
        value: 'cuwx7e491',
      },
      {
        label: '资源位&人群',
        value: 'zlol0zyqs',
      },
      {
        label: '模版&基础',
        value: 'uw4ezn0mn',
      },
      {
        label: '版权平台',
        value: 'sepzqfs4_',
      },
      {
        value: 'i8lepjmyd',
        label: '分发',
      },
      {
        label: '模板框架',
        value: 'i53sd4n3a',
      },
      {
        label: '审核与标注',
        value: '100lndsxh',
      },
      {
        label: '生图',
        value: 'choh1fmjc',
      },
      {
        label: '音乐',
        value: '8ahoxqzaq',
      },
      {
        label: '数字人',
        value: 'eyj3b09n4',
      },
      {
        label: '生视频',
        value: 'g_o64v6x9',
      },
      {
        label: '故事模式',
        value: 'xo6c82b4o',
      },
      {
        label: '画布',
        value: 'saj3jk1uv',
      },
      {
        label: '社区互动',
        value: 'g94_zwo8b',
      },
      {
        label: '基础产品',
        value: 'ag01n5p79',
      },
      {
        label: '分发与审核',
        value: 'le36se07i',
      },
      {
        label: '灵感',
        value: 'xvffuobnq',
      },
      {
        label: '隐私安全',
        value: '7vl7dku_q',
      },
      {
        label: '内容安全',
        value: 'vm9wzsit3',
      },
      {
        label: '安全策略',
        value: 't9gdlxnx1',
      },
      {
        label: '未来创作大赛/短片',
        value: 'ph4rqrkv6',
      },
      {
        label: '增长',
        value: 'dzbgb09wz',
      },
      {
        label: '其他',
        value: 'v0a0ick7h',
      },
      {
        label: '智能成片',
        value: 'yy57w4l5u',
      },
      {
        label: '一键成片',
        value: '30m8ple7_',
      },
      {
        label: '云相册',
        value: '4o_s2ks84',
      },
      {
        label: 'AI能力',
        value: '6rlkdfvwa',
      },
      {
        label: '自媒体',
        value: 'yoj7oi3el',
      },
      {
        label: '基础体验',
        value: 'lmpkax24y',
      },
      {
        label: '头部作者功能',
        value: 'x_ura5xvo',
      },
      {
        label: '功能素材分发',
        value: '4fsztyceo',
      },
      {
        label: '素材生产工具',
        value: 'o5jmll93p',
      },
      {
        value: '_f92yg3tf',
        label: '埋点/锚点等其他非功能需求',
      },
      {
        value: 'gqls6ekc4',
        label: '负向治理&体验优化',
      },
      {
        label: '故事板编辑器',
        value: 'a8klsexkj',
      },
      {
        label: 'AI配旁白 （片刻）',
        value: '44moo6lvh',
      },
      {
        label: '故事成片',
        value: '34gx15tjn',
      },
      {
        label: 'AI工具（待下线）',
        value: '7cjb7isf8',
      },
      {
        label: '自媒体&素材分发&生产工具（待下线）',
        value: 'kmozbe6hp',
      },
      {
        label: '基础剪辑&特效&文字（待下线）',
        value: 'kg_gc_fb2',
      },
      {
        label: '多端创作-基础编辑',
        value: '995x803ba',
      },
      {
        label: '多端创作-智能编辑',
        value: 'syhor1p5f',
      },
      {
        label: '多端创作-性能画质',
        value: 'amgwx45db',
      },
      {
        label: '多端创作-架构',
        value: '12kc99cld',
      },
      {
        label: '特效-图像与多端技术',
        value: 'apmyfqxgv',
      },
      {
        value: '8kugkjj1m',
        label: '特效-引擎工具',
      },
      {
        label: '特效-特效素材',
        value: 'l9_xryao5',
      },
    ],
  },
  {
    field_key: 'supported_apps',
    options: [
      {
        label: 'App-剪映',
        value: 'e0ecrfhkb',
      },
      {
        label: 'App-CapCut',
        value: 'huzrzdapo',
      },
      {
        label: 'Web-剪映',
        value: '3sqv5813g',
      },
      {
        label: 'Web-CapCut',
        value: 's511otonb',
      },
      {
        label: 'Web-Dreamina',
        value: 'f9cqkujuv',
      },
      {
        label: 'APP-Dreamina',
        value: '7mq_tuj8g',
      },
      {
        label: 'APP-即梦',
        value: 'z1x09_82k',
      },
      {
        label: 'Web-即梦',
        value: 'z1x09_82k',
      },
      {
        value: 'cy8tz6j9t',
        label: '醒图',
      },
      {
        label: 'Hypic',
        value: 'octz78eqj',
      },
      {
        label: 'PC-剪映专业版',
        value: 'cn97ku7z0',
      },
      {
        label: 'PC-CapCut专业版',
        value: 'vjnatptn2',
      },
      {
        label: 'Web-CapCut电商版',
        value: '8y2yvnwxe',
      },
      {
        label: 'App-抖音',
        value: 't2ke9tlh8',
      },
      {
        label: 'App-抖音极速版',
        value: '6a9_keut8',
      },
      {
        label: 'App-抖音火山版',
        value: 'w6xm3hwf3',
      },
      {
        label: 'App-抖音精选',
        value: 'h_pdpcb8j',
      },
      {
        value: '0ifyvqsr2',
        label: 'TikTok',
      },
      {
        label: 'vimo',
        value: 'gswjmif67',
      },
      {
        label: '多媒体SDK',
        value: 'ofmu67slb',
      },
      {
        label: '多媒体素材',
        value: 'cqbelo3b2',
      },
      {
        label: '纸飞机',
        value: 'ontierjl0',
      },
      {
        label: 'Web-智能成片',
        value: 'obz3y3eea',
      },
      {
        label: 'LV',
        value: '0',
      },
      {
        label: 'FaceU',
        value: '1',
      },
      {
        label: '轻颜',
        value: '2',
      },
      {
        label: '有咔',
        value: '4',
      },
      {
        label: 'Faceubot',
        value: '32',
      },
    ],
    field_name: '上线应用',
  },
  {
    field_key: 'field_74de8f',
    options: [
      {
        label: '中国区',
        value: 'dJcGYMDLg',
      },
      {
        value: '6AMgSe2mm',
        label: '非中区',
      },
    ],
    field_name: '上线区域',
  },
  {
    field_key: 'field_39ef9f',
    field_name: '业务标签',
    options: [
      {
        label: '会员产品',
        value: 'sfv_oz43h',
        children: [
          {
            label: '会员链路',
            value: 'xlg6ldldn',
          },
          {
            label: '会员分层',
            value: 'f4jp3g4_v',
          },
          {
            label: '安全合规',
            value: 'oov_9wjfm',
          },
          {
            value: 's6uk26oi5',
            label: '后台基建',
          },
          {
            label: '降负向',
            value: 'thrmlna_n',
          },
          {
            label: '云/协作/团队',
            value: 'mpdiv6a39',
          },
        ],
      },
      {
        label: '会员权益',
        value: '19v06eq59',
        children: [
          {
            label: '模板权益',
            value: 'ot_dhf3j6',
          },
          {
            label: '功能权益',
            value: '12nqzxm_i',
          },
          {
            label: '素材权益',
            value: '1nyqafhut',
          },
          {
            value: 'ote04r1yh',
            label: '图像权益',
          },
          {
            label: '云盘包',
            value: 'ars7tgm8b',
          },
          {
            value: 'o5r3wuy58',
            label: 'ai权益',
          },
        ],
      },
      {
        value: 'dbh0jvaai',
        children: [
          {
            label: '价格营销',
            value: 'gvfs81vxc',
          },
          {
            label: '会员渗透',
            value: '7ah0a2sqx',
          },
          {
            label: '用户策略',
            value: '27dpch1it',
          },
          {
            label: '站外渠道',
            value: 'huj2q69s9',
          },
        ],
        label: '会员增长',
      },
      {
        children: [
          {
            label: 'PC',
            value: 'ih29zgrgj',
          },
          {
            label: '云+协作',
            value: 't03ec5mu3',
          },
          {
            label: '商用素材',
            value: '1rrd04o9u',
          },
          {
            label: '营销',
            value: 'njdybfyb7',
          },
          {
            label: '功能权益',
            value: '0auosaq0n',
          },
          {
            label: '商用权益',
            value: 'hmuchz0z1',
          },
          {
            value: 'as9ofwcx2',
            label: '权益组合包装',
          },
          {
            label: '其他',
            value: '61_zcbvrf',
          },
        ],
        label: '高价值权益',
        value: '6hv6mkgxb',
      },
      {
        label: '广告',
        value: 'avbte5lb9',
        children: [
          {
            label: '品牌广告',
            value: 'x30wf6il6',
          },
          {
            label: '激励广告',
            value: 'be2e3q3pi',
          },
          {
            label: '预算效率',
            value: 'xn7rvvtpg',
          },
          {
            label: '标准广告流量策略',
            value: 'f9jh29is1',
          },
          {
            label: '数据基建',
            value: '39_e71riu',
          },
        ],
      },
      {
        label: '专项',
        value: 'odk7u4y2n',
      },
      {
        value: 'p0szh3uoy',
        children: [
          {
            label: '特效素材',
            value: 's1dqhbddx',
            children: [
              {
                label: '画面特效',
                value: 'szf1o5u3x',
              },
              {
                label: '人物特效',
                value: '6tfxhcorq',
              },
              {
                label: '转场',
                value: 'ihiz25o3y',
              },
              {
                label: '文字动画',
                value: '3s51dykst',
              },
              {
                label: '视频动画',
                value: '1wqapvv98',
              },
              {
                label: '字幕动画',
                value: 'jop340bxu',
              },
              {
                label: 'lumi原子能力',
                value: '4kwwwnp5o',
              },
              {
                label: '滤镜',
                value: '_4oojlk10',
              },
              {
                label: '美妆',
                value: 'zfqvy4yoh',
              },
              {
                label: '相机-原生模式',
                value: 'iutczqhg_',
              },
              {
                label: '相机-风格模式',
                value: '_6140gvqf',
              },
              {
                value: 'qxh17oe4e',
                label: '相机-创意模式',
              },
              {
                label: '歌词动效',
                value: '20i4k7xjl',
              },
              {
                label: '公共事务',
                value: '538xpxjlx',
              },
            ],
          },
          {
            label: '引擎工具',
            value: '3stk9m4lb',
          },
          {
            label: '图像与多端',
            value: '9n1izxb3s',
          },
        ],
        label: '特效',
      },
      {
        label: '音视频',
        value: 'gssun4qbm',
        children: [
          {
            value: '_czx3i1kf',
            label: 'WebCC',
          },
          {
            label: '鸿蒙',
            value: 'ek49c727w',
          },
        ],
      },
      {
        label: '多端创作',
        value: 'vvx3dbz41',
        children: [
          {
            label: '智能编辑',
            value: 'r2dpxa5zq',
          },
          {
            label: '基础编辑',
            value: 'e9ilru1k6',
          },
          {
            label: '性能画质',
            value: '4d23agqyi',
          },
          {
            label: '架构',
            value: 'r82tqqmp_',
          },
        ],
      },
      {
        label: 'UGC&工具增长',
        value: 'pp4yok7eg',
      },
      {
        label: '营销方向',
        value: '5pxhp2m10',
      },
      {
        value: 'vng63m0de',
        label: 'PGC',
      },
      {
        value: '2w5p1ja24',
        label: '编辑器及特效产品',
      },
      {
        label: '产品框架-工具',
        value: 'je1paj2wu',
      },
      {
        label: '产品框架-生态',
        value: 'o4uvf7gt1',
      },
      {
        label: '分发策略',
        value: 'wnkmweo2t',
      },
      {
        label: '生态产品',
        value: 'ys0obbnys',
      },
      {
        label: '作者产品',
        value: 'f8zvhdtui',
      },
      {
        label: '模版体验',
        value: 's9a3bnwz4',
      },
      {
        label: 'AI剪辑',
        value: '_gfvk0bup',
      },
      {
        label: '云相册',
        value: 'c4g9qs6la',
      },
      {
        label: '即/D',
        value: '5vyfuxefr',
        children: [
          {
            label: '工具',
            value: '5sd6lvjxr',
          },
          {
            label: '分发',
            value: '0xeuynz2x',
          },
          {
            label: '社区',
            value: 'd8jraoc19',
          },
          {
            label: '生图',
            value: 'fwc27k7km',
          },
          {
            label: '音乐',
            value: '5i26x0kil',
          },
          {
            label: '数字人',
            value: '1kk7ke6sp',
          },
          {
            label: '生视频',
            value: '9rjxdt1pa',
          },
          {
            value: '14n2yo9hw',
            label: '故事模式',
          },
          {
            label: '画布',
            value: 'pbitxhxmj',
          },
          {
            label: '社区互动',
            value: 'zv02q78nx',
          },
          {
            label: '基础产品',
            value: '8km4n43w0',
          },
          {
            label: '分发与审核',
            value: '09yjwcg1c',
          },
          {
            label: '灵感',
            value: '4_03e_7fs',
          },
          {
            label: '隐私安全',
            value: '416obo2dw',
          },
          {
            label: '内容安全',
            value: 'wdvscapo0',
          },
          {
            value: 'gn14lun8l',
            label: '安全策略',
          },
          {
            label: '未来创作大赛/短片',
            value: '51mzc56ew',
          },
          {
            label: '增长',
            value: '04rw1lr6p',
          },
          {
            label: '其他',
            value: 'qqercbzhw',
          },
        ],
      },
      {
        label: '内容-审核与标注',
        value: 'eg1jc3l4b',
      },
      {
        label: '内容-模板框架',
        value: 'jxcqprxdw',
      },
      {
        label: '内容-分发',
        value: '1eykj3h0u',
      },
      {
        label: 'vimo',
        value: '7rh3xz1ph',
        children: [
          {
            value: '8nmuun143',
            label: '素材',
          },
          {
            label: '作者',
            value: '3uyut4h9u',
          },
          {
            label: '资源位&人群',
            value: 'l7l1rm1qa',
          },
          {
            label: '模板&基础',
            value: 'lrz1an4j3',
          },
          {
            label: '版权平台',
            value: '3hnbhwzio',
          },
        ],
      },
      {
        label: '抖T联动',
        value: 'tt0fse49p',
        children: [
          {
            label: '锚点',
            value: 'rm46_zz5q',
          },
          {
            label: '触达承接',
            value: '0bjsnff9a',
          },
          {
            label: '运营',
            value: '6qht8aurg',
          },
        ],
      },
      {
        label: 'AI基础建设',
        value: 'drwvp1im2',
      },
      {
        value: '__wx5yj4j',
        label: '工具',
      },
      {
        label: '主框架',
        value: 'khtikefft',
      },
      {
        value: 'f3gtc0eao',
        label: 'AI特效',
      },
      {
        label: '基础技术',
        value: '7ufmuj_rt',
        children: [
          {
            value: 'dxot6l25l',
            label: '纸飞机研效平台',
          },
          {
            label: '质量优化',
            value: '_0hleg37o',
          },
          {
            label: '性能体验优化',
            value: 'o_xoxns32',
          },
          {
            label: '工程架构',
            value: 'd5vdejbk2',
          },
        ],
      },
      {
        label: '视频工具',
        value: 'id0gd0j1m',
      },
      {
        label: '图片工具',
        value: 'cv1e4l5c6',
      },
      {
        label: '商业化',
        value: '_yep0in82',
      },
      {
        value: '3m2be7jpa',
        label: '主框架',
      },
      {
        label: '营销工具-增长',
        value: 'w7u96_jbn',
      },
      {
        label: '编辑器基础产品',
        value: '5lche26cd',
      },
      {
        label: '特效&素材生产工具',
        value: 'hfcvij5gk',
      },
      {
        label: '智能成片',
        value: 'ogvdsytgb',
      },
      {
        label: '一键成片',
        value: '03xdd1gzz',
      },
      {
        label: 'AI配旁白',
        value: 'elx5aq3s9',
      },
      {
        value: 'ausbhw_2f',
        label: '故事成片',
      },
      {
        label: '故事板编辑器',
        value: 'fo5zp22yf',
      },
      {
        label: 'AI能力',
        value: 'm8btxmh6u',
      },
      {
        value: '476qojrxz',
        label: '自媒体',
      },
      {
        label: '头部作者功能',
        value: 'zpe57kyiz',
      },
      {
        label: '功能素材分发',
        value: 'su48c93jj',
      },
      {
        label: '埋点/锚点等其他非功能',
        value: 'xmcy4iutx',
      },
      {
        label: '负向治理&体验优化',
        value: 'l_20tji8r',
      },
    ],
  },
  {
    field_key: 'field_16bce2',
    field_name: '需求来源业务线',
    options: [
      {
        label: '视频工具与素材',
        value: 'x1fo9s8a2',
        children: [
          {
            label: '素材工具',
            value: 'f75q7m9aw',
          },
          {
            label: '视频工具',
            value: 'hawd_1y7l',
          },
          {
            label: '素材分发',
            value: '48coav9ii',
          },
        ],
      },
      {
        label: '醒图Hypic',
        value: 'yz39o19x5',
        children: [
          {
            label: '工具',
            value: 'sfxqlxfx9',
          },
          {
            label: '模板',
            value: 'apvzng6bl',
          },
          {
            value: '2hmhao2z9',
            label: '主框架',
          },
          {
            value: 'o3vx27fvq',
            label: 'AI创新',
          },
          {
            label: '人像&图片编辑',
            value: 'xj3johp8w',
          },
          {
            label: '编辑器主框架和策略',
            value: '5ypzlxoy2',
          },
        ],
      },
      {
        children: [
          {
            value: 'b0pbtoqya',
            label: '作者',
          },
          {
            label: '模板框架与分发',
            value: 'ga3h9sydq',
          },
          {
            label: '模板工具',
            value: '_0a29sblm',
          },
        ],
        label: '内容生态',
        value: '4_l4z2ylr',
      },
      {
        label: '主框架与基础产品',
        value: 'g0d3z3wm6',
      },
      {
        label: '智能成片',
        value: 'ygf1rhnmg',
      },
      {
        label: '即梦Dreamina',
        value: 'yreo_79s9',
        children: [
          {
            value: 'ha8jmfc4y',
            label: 'AI视频',
          },
          {
            label: 'AI生图',
            value: '0k66w8y7q',
          },
          {
            label: 'AI主框架',
            value: 'znyr7oypj',
          },
          {
            label: '用户社区',
            value: '3vtvoku1c',
          },
          {
            label: '内容平台',
            value: 'lxb2575_s',
          },
        ],
      },
      {
        label: '商业化',
        value: 'idq2c6ybz',
        children: [
          {
            label: '订阅',
            value: 'fmcbayfy8',
          },
          {
            label: '广告',
            value: 'm5lg20z_e',
          },
          {
            label: '经营分析',
            value: 'th3povqeu',
          },
        ],
      },
      {
        label: 'UG',
        value: '0kfv3q97x',
        children: [
          {
            label: '抖T联动',
            value: '6novlcbpe',
          },
          {
            label: 'SEO增长',
            value: 'pf3z5_pe5',
          },
          {
            label: '社媒增长',
            value: 'w9sqsykh9',
          },
          {
            label: '剪映UG',
            value: 'nvuwp7sgm',
          },
          {
            label: 'Capcut UG',
            value: '3ht73h1ws',
          },
          {
            label: 'Dreamina UG',
            value: 'r64v4v4o9',
          },
        ],
      },
      {
        label: 'vimo与素材库',
        value: 'mvk6nt8ja',
        children: [
          {
            label: 'vimo后台',
            value: 'ji7vmpnso',
          },
          {
            label: '版权平台',
            value: 'atyhvzm8b',
          },
        ],
      },
      {
        label: '营销工具',
        value: 's_lor03hq',
        children: [
          {
            label: '权益方案',
            value: 'enhxgkpof',
          },
          {
            label: '产品核心能力',
            value: 'ym8pcdoe4',
          },
        ],
      },
      {
        label: '安全合规',
        value: 'l0n_xun6m',
        children: [
          {
            label: '中国区-安全合规',
            value: '3b6tdn3da',
          },
          {
            label: '非中区-安全合规',
            value: 'rq0wt9hi7',
          },
        ],
      },
      {
        label: '多媒体',
        value: 'pqtf72ios',
      },
      {
        label: '剪映',
        value: 'g5fcj6gvs',
        children: [
          {
            label: 'UGC与工具增长',
            value: '0ufz2p07v',
          },
          {
            value: '12sx_vwj2',
            label: 'PGC',
          },
          {
            label: '编辑器及特效产品',
            value: '684_2_lk9',
          },
          {
            label: '内容生态与分发',
            value: 'o830whuij',
          },
          {
            label: 'AI剪辑',
            value: '_htlsio5q',
          },
        ],
      },
      {
        label: 'CapCut',
        value: 'gyr2gs52r',
        children: [
          {
            label: 'AI特效开放',
            value: 'ya5wuz71a',
          },
          {
            label: '模板工具与分发',
            value: '8v22rbk71',
            children: [
              {
                label: '模板框架与体验',
                value: '6jtn068g2',
              },
              {
                value: 'orx1lfnnn',
                label: ' 搜推分发',
              },
              {
                label: '生态基建',
                value: '4e83sg66s',
              },
            ],
          },
          {
            label: '多轨工具',
            value: '8226_cyn3',
            children: [
              {
                label: '文本编辑器',
                value: 'p22sticu6',
              },
              {
                value: 'geqwnffwi',
                label: '多轨AI',
              },
              {
                label: '基础编辑器',
                value: '3rx0g9sp4',
              },
              {
                label: 'CC智能成片',
                value: 'zjxhfapg9',
              },
            ],
          },
          {
            label: '主框架与Web ',
            value: '1zgurqzuu',
          },
          {
            label: '营销策略',
            value: 'hm0jo52dn',
          },
          {
            label: '内容与作者平台',
            value: 'x7p2cc_ps',
            children: [
              {
                label: '作者策略',
                value: 'kkfof8c22',
              },
              {
                label: '素材生产与分发',
                value: 'vpa78zes5',
              },
            ],
          },
        ],
      },
      {
        label: '基础技术',
        value: 'gk76a11_p',
      },
      {
        label: '服务架构',
        value: '4fuvl0_sg',
      },
      {
        label: 'meego试用业务线',
        value: 'e0k98y_df',
      },
    ],
  },
];
