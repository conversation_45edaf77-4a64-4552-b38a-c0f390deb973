import { Avatar, User } from '@pa/shared/dist/src/core';

export enum StoryRevenueTaskInfoEditResultType {
  Success = 0,
  Conflict = 1,
  UnknownError = 2,
}

// 复盘文档编辑的结果枚举
export enum StoryRevenueReviewDocEditResultType {
  Success = 0,
  Conflict = 1,
  InvalidDoc = 2,
  UnknownError = 3,
}

export enum StoryRevenueTaskSubExperimentStatus {
  Ended = 0, // 已结束
  InProgress = 1, // 进行中
  ToBeScheduled = 2, // 待调度
  InDebug = 3, // 调试中
  Paused = 4, // 已暂停
  ToBeScheduledEnded = 5, // 待调度结束
  Released = 91, // 已上线，效果等同于 0
}

export enum StoryRevenueTaskSubExperimentBindType {
  FromLibra = 0, // 从 Libra 绑定
  FromPaperAirplane = 1, // 从纸飞机绑定
}

export enum StoryRevenueTaskRevenueReviewType {
  ParticipateInTheEvaluation = 0, // 参与评估（能AA/AB严谨量化表现）
  NotParticipateForExperimentNotOpen = 1, // 不参与-优化迭代但未开实验
  NotParticipateForNoConclusionYet = 2, // 不参与-已开实验但尚无结论
  NotParticipateForBugFix = 3, // 不参与-bug修复（2025.03.12 废弃）
  NotParticipateForSecurityCompliance = 4, // 不参与-安全合规降负反馈类（2025.03.12 废弃）
  NotParticipateForDataInfrastructure = 5, // 不参与-数据基建/产品基建
  NotParticipateForStoryUnderDevelopment = 6, // 不参与-需求开发中
  NotParticipateForStoryIsPending = 7, // 不参与-需求pending
  NotParticipateForLongTermCapacityBuilding = 8, // 不参与-长期建设能力（后续需要参与评估）
  NotParticipateForNegativeIssueGovernanceAndExperienceOpt = 9, // 不参与-负向问题治理&体验优化
}

export enum StoryRevenueTaskRevenueExpectationType {
  ExceedExpectations = 0, // 超出预期
  MeetExpectations = 1, // 符合预期
  BelowExpectations = 2, // 低于预期
}

export enum StoryRevenueTaskRevenueSelfAssessmentType {
  BelowExpectationsAndContinuousOptimization = 0, // 收益不及预期-后续继续优化（产品决策正常）
  BelowExpectationsAndStopTrying = 1, // 收益不及预期-后续停止尝试（产品决策正常）
  BelowExpectationsForPMWrongDecision = 2, // 收益不及预期-产品决策失误
  MeetExpectationsForNegativeGovernance = 3, // 符合预期-负向治理
  MeetExpectationsForLongTermValue = 4, // 符合预期-长期价值（长期收益更大）
  MeetExpectationsForLongTermExploration = 5, // 符合预期-长期探索（AI类需要保持耐心探索）
  MeetExpectationsForVerticalScenario = 6, // 符合预期-垂直场景（高级功能/渗透低的场景，不预期撬动端or模块）
  ExceedExpectations = 7, // 超出预期
}

export enum StoryRevenueTaskStatus {
  PendingEvaluation = 0, // 待评估
  UnderEvaluation = 1, // 评估中
  Archived = 2, // 已归档
}

export enum StoryRevenueSignificanceType {
  None = 0, // 不显著
  Positive = 1, // 正向显著
  Negative = 2, // 负向显著
}

export enum StoryRevenueConclusionType {
  PlatformRevenue = 0, // 端收益
  ModuleRevenue = 1, // 模块收益
  KeyProcessRevenue = 2, // 关键过程收益
}

export enum StoryRevenueBindLibraResultType {
  Success = 0,
  QueryNewestTaskInfoFailed = -1,
  NeedRefreshBeforeUpdateBind = -2,
  UpdateBindInfoFailed = -3,
}

export enum StoryRevenueTaskBenefitsRefreshType {
  ByHive = 0, // 通过 Hive 表方式刷新单条 task 收益数据（更快速，但是数据可能不准确，并且是 t-4 的数据）
  ByLibraApi = 1, // 通过 Libra API 方式刷新单条 task 收益数据（更准确，但是刷新比较慢）
}

export enum StoryRevenueLTMetricLossType {
  None = 0, // 无
  Experience = 1, // 体验类损失
  Business = 2, // 商业化类损失
}

// 2024-Q4 对 User 进行了重构，要求 open_id 必填。但是需求收益历史的数据有很多没有填写 open_id，此处兼容一下
// 重构 MR：https://bits.bytedance.net/devops/6735061506/develop/detail/842978/change/1382196?devops_space_type=server_fe&dv_filepath=apps%2Fquality%2Fshared%2FstoryRevenueReviewPlatform%2FStoryRevenuePlatformUtils.ts
export interface CopyUser {
  name: string;
  email?: string;
  open_id?: string;
  user_id?: string;
  avatar?: string | Avatar;
}

// 需求收益 Task 子信息 -- Meego 需求信息（StoryRevenueTaskMeegoInfo）
export interface StoryRevenueTaskMeegoInfo {
  id: string; // Meego Id
  url: string; // Meego 链接
  name: string; // 需求名称
  owners: CopyUser[]; // 需求 Owner（可多人）
  creator: CopyUser; // 需求创建人
  type: string; // 需求类型（产品需求、技术需求等）
  status: string; // 需求状态
  primaryBusiness: string; // 一级业务线
  secondaryBusiness: string; // 二级业务线
  submodules: string[]; // 子模块（多选）
  publishedRegions: string[]; // 上线区域（多选）
  publishedApps: string[]; // 上线应用（多选）
  singleAppReason?: string; // 只有一个上线应用时，需要补充仅上单端的原因
  prdUrl: string; // 需求文档
  rdWorkload: number; // RD 工作量
  relatedRDTeams: string[]; // 涉及研发团队（多选）
  techOwners: CopyUser[]; // 技术 Owner（多选）
  testFinishedTime: number; // 需求测试完成时间
  internalReviewFinishedTime: number; // 需求内审完成时间
  priority: string;
  detailReviewFinishedTime?: number; // 需求详评完成时间
  algorithmRelated?: boolean; // 和算法相关
  bizTag?: {
    primary: string;
    secondary: string;
  }[]; // 业务标签
  sourceBusiness?: {
    primary: string;
    secondary: string;
  }[]; // 需求来源业务线
  description?: string; // 需求描述
}

// 需求收益 Task 子信息 -- 实验信息（StoryRevenueTaskExperimentInfo）
export interface StoryRevenueTaskExperimentInfo {
  subExperimentCount: number; // 子实验数量
  subExperimentList: StoryRevenueTaskSubExperimentInfo[]; // 子实验列表
}

export interface StoryRevenueTaskSubExperimentInfo {
  libraFlightId: string; // 关联的实验 id
  libraUrl: string; // 关联实验链接
  libraTitle: string;
  libraOwners: CopyUser[];
  status: StoryRevenueTaskSubExperimentStatus; // 关联实验状态
  startTime: number; // 实验开启时间
  endTime: number; // 实验关闭时间
  duration: number; // 实验持续时间
  revenueInfo?: StoryRevenueTaskRevenueInfo; // 实验收益信息
  bindType?: StoryRevenueTaskSubExperimentBindType; // 绑定类型
  publishedApp?: string; // 上线应用（从 Libra 原信息获取，然后映射出来）
  markAsAnalysisBasis?: boolean; // 是否为分析依据
}

// 需求收益显著性指标信息
export interface StoryRevenueTaskRevenueSignificanceInfo {
  metricGroupId: string; // 指标组 id
  metricGroupName: string; // 指标组名称
  metricId: string; // 指标 id
  metricIdName: string; // 指标名称
  significance: StoryRevenueSignificanceType; // 显著类型（不显著、正向显著、负向显著）
  relativeDiff: string; // 相对差
  absoluteDiff: string; // 绝对差
  pValue: string; // p 值
  experimentalVid: string; // 实验组 Id
  benchmarkVid: string; // 对照组 Id
}

// 需求收益结论信息（由纸飞机自动计算完成）
export interface StoryRevenueTaskRevenueConclusionInfo {
  vid: string; // 实验组 Id
  hasRevenue: boolean; // 是否有收益
  type: StoryRevenueConclusionType; // 收益类型（端收益、模块收益、关键过程收益）
  significanceValues: StoryRevenueTaskRevenueSignificanceInfo[]; // 显著性指标
}

// 需求收益 Task 子信息 -- 收益信息（StoryRevenueTaskRevenueInfo）
export interface StoryRevenueTaskRevenueInfo {
  reviewPeriodId: string; // 所属的 Review 周期
  dsReviewer?: CopyUser[]; // DS Review人
  reviewDocUrl?: string; // 收益复盘文档
  reviewType?: StoryRevenueTaskRevenueReviewType; // Review type（参与 or 不参与，不参与包含多种原因）
  noConclusionReason?: string; // review type为无回收结论时，需要补充原因
  expectationType?: StoryRevenueTaskRevenueExpectationType; // 预期类型（超出预期、符合预期、低于预期）
  hasPlatformRevenue?: boolean; // 是否有端收益
  platformRevenueHasDiff?: boolean; // 端收益表现不一致
  manullyHasPlatformRevenue?: boolean; // 「人工判断」是否有端收益
  dsHasPlatformRevenue?: boolean; // 「DS判断」是否有端收益
  platformRevenueMetricConclusion?: string; // 端收益指标结论
  platformRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo[]; // 端收益指标结论(纸飞机自动计算)，有该字段则结论不可编辑
  hasModuleRevenue?: boolean; // 是否有模块收益
  manullyHasModuleRevenue?: boolean; // 「人工判断」是否有模块收益
  dsHasModuleRevenue?: boolean; // 「DS判断」是否有模块收益
  moduleRevenueMetricConclusion?: string; // 模块收益指标结论
  moduleRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo[]; // 模块收益指标结论(纸飞机自动计算)，有该字段则结论不可编辑
  hasKeyProcessRevenue?: boolean; // 是否有关键过程收益
  manullyHasKeyProcessRevenue?: boolean; // 「人工判断」是否有关键过程收益
  dsHasKeyProcessRevenue?: boolean; // 「DS判断」是否有关键过程收益
  keyProcessRevenueMetricConclusion?: string; // 关键过程收益指标结论
  keyProcessRevenueMetricAutoComputedConclusion?: StoryRevenueTaskRevenueConclusionInfo[]; // 关键过程收益指标结论(纸飞机自动计算)，有该字段则结论不可编辑
  dsRevenueConclusionRemark?: string;
  isFullyRelease?: boolean; // 是否上线/推全
  revenueSelfAssessment?: StoryRevenueTaskRevenueSelfAssessmentType; // 收益自评
  remark?: string; // 备注
  hasMetricLoss?: boolean; // 是否有指标损失
  manuallyHasMetricLoss?: boolean; // 「人工判断」是否有指标损失
  ltMetricLossType?: StoryRevenueLTMetricLossType; // 大盘指标折损类型
}

// 上线应用收益信息（在没有任何子实验的情况下由 DS 填写）
export interface StoryRevenuePublishedAppRevenueInfo {
  publishedApp?: string; // 上线应用（从 Libra 原信息获取，然后映射出来）
  markAsAnalysisBasis?: boolean; // 是否为分析依据
  dsHasPlatformRevenue?: boolean; // 「DS判断」是否有端收益
  dsHasModuleRevenue?: boolean; // 「DS判断」是否有模块收益
  dsHasKeyProcessRevenue?: boolean; // 「DS判断」是否有关键过程收益
  dsRevenueConclusionRemark?: string;
}

// 需求收益 Task 总信息（StoryRevenueTaskInfo）
export interface StoryRevenueTaskInfo {
  _id?: string; // 需求收益 Task 唯一 id（数据库自动生成）
  terminated: boolean; // Task 是否终止
  status: StoryRevenueTaskStatus; // Task 状态（待评估、评估中、已归档）
  reviewPeriodId: string; // 所属的 Review 周期
  reviewDuration: number; // Review 时长
  fillInCompleted: boolean; // Task 是否填写完整
  meegoInfo: StoryRevenueTaskMeegoInfo; // Task Meego 需求信息
  experimentInfo?: StoryRevenueTaskExperimentInfo; // Task 实验信息
  revenueInfo?: StoryRevenueTaskRevenueInfo; // Task 收益信息
  updating?: boolean; // 是否正在更新数据
  lastManuallyBindLibraTime?: number; // 上次手动绑定 Libra 实验的时间
  publishedAppRevenueInfos?: StoryRevenuePublishedAppRevenueInfo[]; // 上线应用收益信息（在没有任何子实验的情况下由 DS 填写）
  revenueInfoCompleted?: boolean; // DS字段是否填写完整
}

export const StoryRevenueConvertCopyUserToUser = (users: CopyUser[]): User[] =>
  users.map(it => ({
    name: it.name,
    email: it.email,
    open_id: it.open_id ?? '',
    user_id: it.user_id,
    avatar: it.avatar,
  }));

// Task Info 修复（部分前端 UI 表单填写可能引发数据错误，比如 bool 类型填成了整型；传入了 ts 的 null 类型等）
export const StoryRevenueTaskInfoFix = (taskInfo: StoryRevenueTaskInfo): StoryRevenueTaskInfo => {
  const newTaskInfo = { ...taskInfo } as StoryRevenueTaskInfo;
  const { revenueInfo, meegoInfo } = newTaskInfo;
  if (revenueInfo) {
    if (revenueInfo.hasPlatformRevenue !== undefined && typeof revenueInfo.hasPlatformRevenue === 'number') {
      revenueInfo.hasPlatformRevenue = Boolean(revenueInfo.hasPlatformRevenue);
    }
    if (revenueInfo.hasModuleRevenue !== undefined && typeof revenueInfo.hasModuleRevenue === 'number') {
      revenueInfo.hasModuleRevenue = Boolean(revenueInfo.hasModuleRevenue);
    }
    if (revenueInfo.hasKeyProcessRevenue !== undefined && typeof revenueInfo.hasKeyProcessRevenue === 'number') {
      revenueInfo.hasKeyProcessRevenue = Boolean(revenueInfo.hasKeyProcessRevenue);
    }
    if (revenueInfo.isFullyRelease !== undefined && typeof revenueInfo.isFullyRelease === 'number') {
      revenueInfo.isFullyRelease = Boolean(revenueInfo.isFullyRelease);
    }
    if (revenueInfo.revenueSelfAssessment !== undefined && typeof revenueInfo.revenueSelfAssessment === 'string') {
      revenueInfo.revenueSelfAssessment = Number(revenueInfo.revenueSelfAssessment);
    }
    if (revenueInfo.hasMetricLoss !== undefined && typeof revenueInfo.hasMetricLoss === 'number') {
      revenueInfo.hasMetricLoss = Boolean(revenueInfo.hasMetricLoss);
    }
  }

  if (meegoInfo.algorithmRelated !== undefined && meegoInfo.algorithmRelated === null) {
    delete meegoInfo.algorithmRelated;
  }

  return newTaskInfo;
};
