import { FilterOptionType, FilterType } from '@shared/utils/conditionFilter';

// 完整性校验字段
export enum TaskIntegrationCheckType {
  NotCheck = 0,
  Neccessary = 1, // 必须校验
  ReviewNeccessary = 2, // 参与Review时必须校验
}

export interface ColumnCustomStatus {
  [key: string]: {
    originEnable: boolean;
    selectedEnable: boolean;
  };
}

export interface StoryRevenueTableColumnFilterConfig {
  filter_type: FilterType;
  key_path: string[];
  value_type: FilterOptionType;
  raw_array?: boolean;
}

export interface StoryRevenueTableColumnConfig {
  column_name: string;
  column_id: string;
  lock_enable: boolean;
  default_enable: boolean;
  integration_check_type?: TaskIntegrationCheckType;
  enable: boolean;
  filter_config?: StoryRevenueTableColumnFilterConfig;
}

export interface StoryRevenueTableColumnGroup {
  group_name: string;
  group_id: string;
  configs: StoryRevenueTableColumnConfig[];
}

export interface StoryRevenueTableColumnUserSetting {
  user_email: string;
  groups: StoryRevenueTableColumnGroup[];
}

export const StoryRevenueTableColumnDefaultUserSetting = (): StoryRevenueTableColumnUserSetting => ({
  user_email: '',
  groups: [
    {
      group_name: '需求信息',
      group_id: 'feature_info',
      configs: [
        {
          column_name: '需求名称',
          column_id: 'feature_name',
          lock_enable: true,
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeText,
            key_path: ['meegoInfo.name'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '操作',
          column_id: 'operation',
          lock_enable: true,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '需求Owner',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          column_id: 'feature_owner',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeUser,
            key_path: ['meegoInfo.owners.email'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '需求描述&目标',
          column_id: 'feature_description',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeText,
            key_path: ['meegoInfo.description'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '创建人',
          column_id: 'feature_creator',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '需求来源业务线',
          column_id: 'source_business',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeTreeEnum,
            key_path: ['meegoInfo.sourceBusiness.primary', 'meegoInfo.sourceBusiness.secondary'],
            value_type: FilterOptionType.TypeString,
            raw_array: true,
          },
        },
        {
          column_name: '业务线',
          column_id: 'primary_business',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeTreeEnum,
            key_path: ['meegoInfo.primaryBusiness', 'meegoInfo.secondaryBusiness'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '业务标签',
          column_id: 'biz_tag',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeTreeEnum,
            key_path: ['meegoInfo.bizTag.primary', 'meegoInfo.bizTag.secondary'],
            value_type: FilterOptionType.TypeString,
            raw_array: true,
          },
        },
        {
          column_name: '是否和算法相关',
          column_id: 'algorithm_related',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['meegoInfo.algorithmRelated'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        // {
        // 	column_name: "子模块",
        // 	column_id: "sub_modules",
        // 	lock_enable: false,
        // 	default_enable: true,
        // 	enable: true,
        // 	filter_config: {
        // 		filter_type: FilterType.FilterTypeEnum,
        // 		key_path: ["meegoInfo.submodules"],
        // 		value_type: FilterOptionType.TypeString,
        // 		raw_array: true,
        // 	},
        // },
        {
          column_name: '优先级',
          column_id: 'feature_priority',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['meegoInfo.priority'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '上线区域',
          column_id: 'published_regions',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['meegoInfo.publishedRegions'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '上线应用',
          column_id: 'published_apps',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['meegoInfo.publishedApps'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '仅上单端的原因',
          // integration_check_type: TaskIntegrationCheckType.Neccessary,
          column_id: 'single_platform_reason',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '需求类型',
          column_id: 'feature_type',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '需求文档',
          column_id: 'feature_doc',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: 'RD工作量',
          column_id: 'rd_work_load',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeNumber,
            key_path: ['meegoInfo.rdWorkload'],
            value_type: FilterOptionType.TypeNumber,
          },
        },
        {
          column_name: '涉及研发团队',
          column_id: 'related_rd_teams',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '技术Owner',
          column_id: 'tech_owner',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '需求状态',
          column_id: 'feature_status',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '需求测试完成时间',
          column_id: 'test_finished_time',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '需求内审完成时间',
          column_id: 'review_finished_time',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: 'Meego Id',
          column_id: 'meego_id',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeText,
            key_path: ['meegoInfo.id'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '需求meego链接',
          column_id: 'meego_url',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
      ],
    },
    {
      group_name: '收益信息',
      group_id: 'revenue_info',
      configs: [
        {
          column_name: '需求收益复盘文档',
          column_id: 'revenue_review_doc',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: 'DS review人',
          column_id: 'ds_reviewer',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeUser,
            key_path: ['revenueInfo.dsReviewer.email'],
            value_type: FilterOptionType.TypeString,
          },
        },
        {
          column_name: '是否参与季度需求收益评估',
          column_id: 'is_review_case',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.reviewType'],
            value_type: FilterOptionType.TypeNumber,
          },
        },
        {
          column_name: '无法回收结论的原因',
          column_id: 'no_conclusion_reason',
          integration_check_type: TaskIntegrationCheckType.Neccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '收益自评',
          column_id: 'is_match_expected',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.expectationType', 'experimentInfo.revenueInfo.expectationType'],
            value_type: FilterOptionType.TypeNumber,
          },
        },
        {
          column_name: '是否为分析依据',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          column_id: 'is_analysis_basis',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '是否为分析依据',
          integration_check_type: TaskIntegrationCheckType.NotCheck,
          column_id: 'has_analysis_basis',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: [
              'publishedAppRevenueInfos.markAsAnalysisBasis',
              'experimentInfo.revenueInfo.markAsAnalysisBasis',
            ],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '有端收益(PM)',
          column_id: 'has_platform_revenue',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: [
              'revenueInfo.hasPlatformRevenue',
              'experimentInfo.revenueInfo.hasPlatformRevenue',
              'revenueInfo.manullyHasPlatformRevenue',
              'experimentInfo.revenueInfo.manullyHasPlatformRevenue',
            ],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '端收益表现不一致',
          column_id: 'platform_revenue_has_diff',
          integration_check_type: TaskIntegrationCheckType.NotCheck,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.platformRevenueHasDiff'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '有端收益(DS)',
          column_id: 'ds_has_platform_revenue',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.dsHasPlatformRevenue'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '端收益指标详情',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          column_id: 'client_revenue_conclusion',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '有模块收益(PM)',
          column_id: 'has_module_revenue',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: [
              'revenueInfo.hasModuleRevenue',
              'experimentInfo.revenueInfo.hasModuleRevenue',
              'revenueInfo.manullyHasModuleRevenue',
              'experimentInfo.revenueInfo.manullyHasModuleRevenue',
            ],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '有模块收益(DS)',
          column_id: 'ds_has_module_revenue',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.dsHasModuleRevenue'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '模块收益指标详情',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          column_id: 'module_revenue_conclusion',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '有关键过程收益(PM)',
          column_id: 'has_key_process_revenue',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: [
              'revenueInfo.keyProcessRevenueMetricConclusion',
              'experimentInfo.revenueInfo.keyProcessRevenueMetricConclusion',
              'revenueInfo.manullyHasKeyProcessRevenue',
              'experimentInfo.revenueInfo.manullyHasKeyProcessRevenue',
            ],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '有关键过程收益(DS)',
          column_id: 'ds_has_key_process_revenue',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.dsHasKeyProcessRevenue'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '过程指标收益详情',
          column_id: 'process_revenue_conclusion',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: 'DS修正收益判断差异说明',
          column_id: 'ds_revenue_conclusion_revenue',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '本次是否计划上线/推全',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          column_id: 'is_full_release',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.isFullyRelease', 'experimentInfo.revenueInfo.isFullyRelease'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '自评原因',
          column_id: 'revenue_self_assessment',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfo.revenueSelfAssessment', 'experimentInfo.revenueInfo.revenueSelfAssessment'],
            value_type: FilterOptionType.TypeNumber,
          },
        },
        {
          column_name: '有指标损失',
          column_id: 'has_metric_loss',
          integration_check_type: TaskIntegrationCheckType.ReviewNeccessary,
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: [
              'revenueInfo.hasMetricLoss',
              'experimentInfo.revenueInfo.hasMetricLoss',
              'revenueInfo.manuallyHasMetricLoss',
              'experimentInfo.revenueInfo.manuallyHasMetricLoss',
            ],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: '备注',
          column_id: 'remark',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '参与review周期',
          column_id: 'review_period_id',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
      ],
    },
    {
      group_name: '实验信息',
      group_id: 'flight_info',
      configs: [
        {
          column_name: '实验数量',
          column_id: 'sub_flight_number',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeNumber,
            key_path: ['experimentInfo.subExperimentCount'],
            value_type: FilterOptionType.TypeNumber,
          },
        },
        {
          column_name: '关联实验链接',
          column_id: 'related_flight_link',
          lock_enable: false,
          default_enable: true,
          enable: true,
        },
        {
          column_name: '关联实验状态',
          column_id: 'related_flight_state',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '关联实验开启时间',
          column_id: 'flight_start_time',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '关联实验关闭时间',
          column_id: 'flight_close_time',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
      ],
    },
    {
      group_name: 'task信息',
      group_id: 'task_info',
      configs: [
        {
          column_name: 'task id',
          column_id: 'task_id',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: 'task终止',
          column_id: 'task_terminated',
          lock_enable: false,
          default_enable: false,
          enable: false,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['terminated'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: 'task版本',
          column_id: 'task_version',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '收益review时长',
          column_id: 'review_duration',
          lock_enable: false,
          default_enable: false,
          enable: false,
        },
        {
          column_name: '填写完整性',
          column_id: 'task_integrity',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['fillInCompleted'],
            value_type: FilterOptionType.TypeBool,
          },
        },
        {
          column_name: 'DS填写完整性',
          column_id: 'ds_task_integrity',
          lock_enable: false,
          default_enable: true,
          enable: true,
          filter_config: {
            filter_type: FilterType.FilterTypeEnum,
            key_path: ['revenueInfoCompleted'],
            value_type: FilterOptionType.TypeBool,
          },
        },
      ],
    },
  ],
});

export const StoryRevenueTableColumnIdSort = (): string[] => [
  'feature_name',
  'operation',
  'feature_owner',
  'feature_description',
  'feature_creator',
  'source_business',
  'primary_business',
  'biz_tag',
  'feature_priority',
  'published_regions',
  'published_apps',
  'feature_doc',
  'related_rd_teams',
  'tech_owner',
  'feature_status',
  'test_finished_time',
  'review_finished_time',
  'rd_work_load',
  'is_review_case',
  'no_conclusion_reason',
  'is_match_expected',
  'revenue_self_assessment',
  'algorithm_related',
  'is_full_release',
  'sub_flight_number',
  'related_flight_link',
  'related_flight_state',
  'flight_start_time',
  'flight_close_time',
  'has_platform_revenue',
  'ds_has_platform_revenue',
  'client_revenue_conclusion',
  'has_module_revenue',
  'ds_has_module_revenue',
  'module_revenue_conclusion',
  'has_key_process_revenue',
  'ds_has_key_process_revenue',
  'process_revenue_conclusion',
  'has_metric_loss',
  'has_analysis_basis',
  'ds_revenue_conclusion_revenue',
  'revenue_review_doc',
  'remark',
  'task_integrity',
  'ds_reviewer',
  'ds_task_integrity',
  'task_id',
  'task_terminated',
  'review_duration',
  'meego_id',
];

export const StoryRevenueSubFlightTableColumnIdSort = (): string[] => [
  'has_platform_revenue',
  'client_revenue_conclusion',
  'has_module_revenue',
  'module_revenue_conclusion',
  'has_key_process_revenue',
  'process_revenue_conclusion',
];

export const StoryRevenueFieldPath2ColumnIdMap = (): Record<string, string> => ({
  'revenueInfo.reviewType': 'is_review_case',
  'revenueInfo.noConclusionReason': 'no_conclusion_reason',
  'meegoInfo.algorithmRelated': 'algorithm_related',
  'meegoInfo.singleAppReason': 'single_platform_reason',
  'revenueInfo.expectationType': 'is_match_expected',
  'revenueInfo.revenueSelfAssessment': 'revenue_self_assessment',
  'revenueInfo.isFullyRelease': 'is_full_release',
});
