import { User } from '@pa/shared/dist/src/core';

export interface FilterStatusTemplate {
  key: string;
  group_type: FilterGroupType;
  status: FilterStatus[];
  ref_users: string[];
  include_terminated?: boolean;
}

export interface FilterConfig {
  filterName: string;
  filterId: string;
  filterType: FilterType;
}

export interface EnumFilterConfig extends FilterConfig {
  filterOptions: { value: string | number; label: string }[];
}

export interface UserFilterConfig extends FilterConfig {}

export interface TextFilterConfig extends FilterConfig {}

export interface NumberFilterConfig extends FilterConfig {}

export interface DateFilterConfig extends FilterConfig {}

export interface TreeEnumOptions {
  label: string;
  value: string | number;
  key: string;
  children: TreeEnumOptions[];
  disabled?: boolean;
}
export interface TreeEnumFilterConfig extends FilterConfig {
  filterOptions: TreeEnumOptions[];
}

export interface FloatCheckBoxFilter extends FilterConfig {}

export interface FilterStatus {
  config?: FilterConfig;
  conditionType?: ConditionType;
}

export interface EnumFilterStatus extends FilterStatus {
  selectedValues?: string[];
}

export interface UserFilterStatus extends FilterStatus {
  selectedUsers?: User[];
}

export interface TextFilterStatus extends FilterStatus {
  inputText?: string[];
}

export interface NumberFilterStatus extends FilterStatus {
  inputNumber?: number;
}

export interface TreeEnumFilterStatus extends FilterStatus {
  selectedValues?: string[];
}

export interface DateFilterStatus extends FilterStatus {
  selectedValues: [string, string];
}

export interface FloatCheckBoxStatus extends FilterStatus {
  checked?: boolean;
}

export enum FilterType {
  FilterTypeUser = 0,
  FilterTypeEnum = 1,
  FilterTypeText = 2,
  FilterTypeNumber = 3,
  FilterTypeTreeEnum = 4,
  FilterTypeFloatCheckBox = 5,
  FilterTypeDate = 6,
}

export enum ConditionType {
  ConditionTypeEqual = 0,
  ConditionTypeNotEqual = 1,
  ConditionTypeContain = 2,
  ConditionTypeNotContain = 3,
  ConditionYypeGT = 4, // 大于,
  ConditionTypeGTE = 5, // 大于等于
  ConditionTypeLT = 6, // 小于
  ConditionTypeLTE = 7, // 小于等于
  ConditionTypeIn = 8,
  ConditionTypeNotIn = 9,
  ConditionTypeNull = 10,
  ConditionTypeNotNull = 11,
  ConditionTypeOneToOne = 12, // 特殊条件
}

export enum FilterGroupType {
  And = 0,
  Or = 1,
}

export interface FilterRuleDesc {
  keyPath: string[];
  conditionType: ConditionType;
  targetValue: (string | number | boolean)[];
  rawIsArray?: boolean;
}

export enum FilterOptionType {
  TypeString = 0,
  TypeNumber = 1,
  TypeBool = 2,
}

export const generateFilterRuleQuery = (type: FilterGroupType, ruleDescs: FilterRuleDesc[]): { [key: string]: any } => {
  const ruleSubQuerys: { [key: string]: any }[] = [];
  ruleDescs.forEach(it => {
    if (it.conditionType === ConditionType.ConditionTypeEqual) {
      const inQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const inQuery: { [key: string]: any } = {};
        // const relatedKps = kp.split("&");
        // if (relatedKps.length > 1) {
        //
        // }
        inQuery[kp] = { $in: it.targetValue };
        inQuerys.push(inQuery);
      });
      ruleSubQuerys.push({
        $or: inQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeOneToOne) {
      const inQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach((kp, index) => {
        const inQuery: { [key: string]: any } = {};
        // const relatedKps = kp.split("&");
        // if (relatedKps.length > 1) {
        //
        // }
        inQuery[kp] = { $in: [it.targetValue[index]] };
        inQuerys.push(inQuery);
      });
      ruleSubQuerys.push({
        $and: inQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeNotEqual) {
      const ninQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const ninQuery: { [key: string]: any } = {};
        ninQuery[kp] = { $nin: it.targetValue };
        ninQuerys.push(ninQuery);
      });
      ruleSubQuerys.push({
        $and: ninQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeIn) {
      const inQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const inQuery: { [key: string]: any } = {};
        inQuery[kp] = { $in: it.targetValue };
        inQuerys.push(inQuery);
      });
      ruleSubQuerys.push({
        $or: inQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeNotIn) {
      const ninQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const ninQuery: { [key: string]: any } = {};
        ninQuery[kp] = { $nin: it.targetValue };
        ninQuerys.push(ninQuery);
      });
      ruleSubQuerys.push({
        $and: ninQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeContain) {
      const regexQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const regexQuery: { [key: string]: any } = {};
          regexQuery[kp] = { $regex: tv };
          regexQuerys.push(regexQuery);
        });
      });
      ruleSubQuerys.push({
        $or: regexQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeNotContain) {
      const notRegexQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const notRegexQuery: { [key: string]: any } = {};
          notRegexQuery[kp] = { $not: { $regex: tv } };
          notRegexQuerys.push(notRegexQuery);
        });
      });
      ruleSubQuerys.push({
        $and: notRegexQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionYypeGT) {
      const gtQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const gtQuery: { [key: string]: any } = {};
          gtQuery[kp] = { $gt: tv };
          gtQuerys.push(gtQuery);
        });
      });
      ruleSubQuerys.push({
        $and: gtQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeGTE) {
      const gteQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const gteQuery: { [key: string]: any } = {};
          gteQuery[kp] = { $gte: tv };
          gteQuerys.push(gteQuery);
        });
      });
      ruleSubQuerys.push({
        $and: gteQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeLT) {
      const ltQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const ltQuery: { [key: string]: any } = {};
          ltQuery[kp] = { $lt: tv };
          ltQuerys.push(ltQuery);
        });
      });
      ruleSubQuerys.push({
        $and: ltQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeLTE) {
      const lteQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        it.targetValue.forEach(tv => {
          const lteQuery: { [key: string]: any } = {};
          lteQuery[kp] = { $lte: tv };
          lteQuerys.push(lteQuery);
        });
      });
      ruleSubQuerys.push({
        $and: lteQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeNull) {
      const nullQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const notExists: { [key: string]: any } = {};
        notExists[kp] = { $exists: false };
        const emptyArray: { [key: string]: any } = {};
        emptyArray[kp] = { $size: 0 };
        const isNull: { [key: string]: any } = {};
        isNull[kp] = null;
        const nullQuery: { [key: string]: { [key: string]: any }[] } = { $or: [notExists, emptyArray, isNull] };
        nullQuerys.push(nullQuery);
      });
      ruleSubQuerys.push({
        $and: nullQuerys,
      });
    }
    if (it.conditionType === ConditionType.ConditionTypeNotNull) {
      const notNullQuerys: { [key: string]: any }[] = [];
      it.keyPath.forEach(kp => {
        const exists: { [key: string]: any } = {};
        exists[kp] = { $exists: true };
        const notEmptyArray: { [key: string]: any } = {};
        notEmptyArray[kp] = { $not: { $size: 0 } };
        const notNull: { [key: string]: any } = {};
        notNull[kp] = { $ne: null };
        const notNullQuery: { [key: string]: { [key: string]: any }[] } = it.rawIsArray
          ? { $and: [exists, notEmptyArray] }
          : { $and: [exists, notNull] };
        notNullQuerys.push(notNullQuery);
      });
      ruleSubQuerys.push({
        $or: notNullQuerys,
      });
    }
  });
  return type === FilterGroupType.Or ? { $or: ruleSubQuerys } : { $and: ruleSubQuerys };
};
