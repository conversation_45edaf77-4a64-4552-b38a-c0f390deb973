import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';

export enum GroupName {
  LVAndroid = 'LV-Android',
  LVIOS = 'LV-iOS',
  CapcutAndroid = 'capcut_android',
  CapcutIOS = 'capcut_iOS',
}

export const convertAppIdToGroupName = (appId: number, platform?: SlardarPlatformType): GroupName | null => {
  let processedAppId = appId;
  if (platform) {
    const appIdStr = platform === SlardarPlatformType.Android ? `${appId}02` : `${appId}01`;
    processedAppId = parseInt(appIdStr, 10);
  }
  switch (processedAppId) {
    case 177502:
      return GroupName.LVAndroid;
    case 177501:
      return GroupName.LVIOS;
    case 300601:
      return GroupName.CapcutIOS;
    case 300602:
      return GroupName.CapcutAndroid;
    default:
      return null;
  }
};

export enum SelectedName {
  BeforeIntegration = 'before_integration',
  AfterIntegration = 'after_integration"',
  OtherStage = 'other_stage',
}
