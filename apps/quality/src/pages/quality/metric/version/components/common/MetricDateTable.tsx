import React from 'react';
import { Space, Table, TableProps } from 'antd';
import 'antd/dist/reset.css';
import dayjs from 'dayjs';
import { MetricDataType } from '@shared/typings/tea/alarm';
import UserShow from '@/component/UserShow';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import { get_enum_values } from '@shared/utils/tools'; // Import Ant Design styles

export interface MetricTableData extends MetricDataType {
  [time: string]: any;
}

type Props = {
  startTimestamp: number; // s
  endTimestamp: number; // s
  data?: MetricTableData[];
};

// Function to generate date range
const generateDateRange = (start: number, end: number): string[] => {
  const dates: string[] = [];
  let currentDate = dayjs.unix(start);
  const endDate = dayjs.unix(end);
  while (currentDate.isBefore(endDate)) {
    dates.push(currentDate.format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day'); // Move to the next day
  }
  return dates;
};

const MetricDateTable: React.FC<Props> = ({ startTimestamp, endTimestamp, data }) => {
  // Generate date range
  const dates = generateDateRange(startTimestamp, endTimestamp).reverse();
  const INVALIDATE_STRING = '0';

  // Define columns for the table
  const columns: TableProps<MetricTableData>['columns'] = [
    {
      title: '指标名称',
      dataIndex: ['name', 'teaUrl'],
      key: 'name',
      fixed: 'left',
      width: 250,
      render: (_, { name, teaUrl }) => (
        <div style={{ display: 'flex', width: 250 }}>
          <a
            onClick={() => {
              window.open(teaUrl);
            }}
          >
            {name}
          </a>
        </div>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      fixed: 'left',
      width: 150,
      hidden: true,
      render: (_, { owner }) => (
        <Space style={{ width: 150 }}>
          <UserShow email={owner ?? ''} />
        </Space>
      ),
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      fixed: 'left',
      width: 150,
      hidden: true,
      render: (_, { business }) => <>{BusinessName[business ?? Business.MAIN_FRAMEWORK]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, performanceData) => performanceData.business === filterBusiness,
    },
    {
      title: '版本号',
      width: 150,
      key: 'versionCode',
      dataIndex: 'versionCode',
      fixed: 'left',
    },
    ...dates.map((date, index) => ({
      title: date, // Use the date as the column title
      dataIndex: date,
      key: date,
      width: 120,
      onCell: undefined,
      render: (value: number) => <span>{value?.toFixed(6) || INVALIDATE_STRING}</span>, // Display '-' if there's no value
    })),
  ];

  // Prepare data for the table
  const dataSource = data?.map((_data, index) => ({
    key: index,
    ..._data,
  }));

  return (
    <Space
      direction="vertical"
      style={{
        width: '100%',
      }}
    >
      <Table
        dataSource={dataSource}
        columns={columns}
        pagination={false} // Disable pagination if not needed
        bordered={true}
        virtual
        size="middle"
        scroll={{ x: `calc(400px+120*${dates.length}px)`, y: 'max-content' }} // Enable horizontal scroll if needed
      />
    </Space>
  );
};

export default MetricDateTable;
