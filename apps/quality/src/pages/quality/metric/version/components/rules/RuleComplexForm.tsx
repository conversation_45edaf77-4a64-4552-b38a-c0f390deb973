import {
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { AlarmNewRulesContainer, CompareVersionType, ValueType } from '@shared/typings/tea/RuleTypes';
import React, { useEffect, useRef, useState } from 'react';
import { InputNumber } from 'antd';
import { DBTeaMetric } from '../../../../../../../api/model/TeaMetricTable';
import { keyBy } from 'lodash';
import { ProFormInstance } from '@ant-design/pro-form/lib';
import { PlatformType } from '@pa/shared/dist/src/core';
import { getNewRules } from '@api/metricsAlarmSystem';
import { logger } from '@/pages/quality/metric/version/utils/Logger';

const operatorOptions = [
  { label: '=', value: '==' },
  { label: '≠', value: '!=' },
  { label: '≥', value: '>=' },
  { label: '<', value: '<' },
  { label: '≤', value: '<=' },
  { label: '>', value: '>' },
];

const compareVersionOptions = [
  { label: '当前灰度版本', value: CompareVersionType.currentVersion },
  { label: '上一个灰度小版本', value: CompareVersionType.lastSmallVersion },
  { label: '上一周期灰度版本', value: CompareVersionType.lastBigVersion },
];

const valueTypeOptions = [
  { label: '绝对值', value: ValueType.absolute },
  { label: '百分比', value: ValueType.percentage },
];

const currentVersionTypeOptions = [{ label: '绝对值', value: ValueType.absolute }];

export interface RuleComplexFormProps {
  metrics: DBTeaMetric[];
  platformType: PlatformType;
  appId: string;
  onSave?: (rule: AlarmNewRulesContainer) => void;
  currentRule?: AlarmNewRulesContainer;
}

function transformToAlarmRules(values: any): AlarmNewRulesContainer {
  return { ...values } as AlarmNewRulesContainer;
}

const RuleComplexForm: React.FC<RuleComplexFormProps> = ({ metrics, currentRule, appId, platformType, onSave }) => {
  const metricOptions = metrics.map(metric => ({ label: metric.DisplayName, value: metric.Name }));
  const formRef = useRef<ProFormInstance>();
  const [formData, setFormData] = useState<AlarmNewRulesContainer>();

  useEffect(() => {
    logger.info(`currentRule metricName => ${currentRule?.metricName}`);
    setFormData(currentRule);
  }, [currentRule]);

  useEffect(() => {
    formRef.current?.setFieldsValue(formData);
  }, [formData]);

  async function fetchData(metricName: string, _appId: string, _platformType: PlatformType) {
    const rsp = await getNewRules({
      data: {
        metricName,
        appId: _appId,
        platformType: _platformType,
      },
    });
    return rsp.data;
  }

  return (
    <ProForm
      layout="vertical"
      formRef={formRef}
      initialValues={currentRule}
      onFinish={async values => {
        await formRef.current?.validateFields();
        if (onSave) {
          onSave(transformToAlarmRules(values));
        }
        return true;
      }}
    >
      <ProForm.Item name="appId" initialValue={appId} noStyle />
      <ProForm.Item name="platformType" initialValue={platformType} noStyle />
      <ProForm.Item name="displayName" noStyle />
      <ProForm.Item name="owner" noStyle />
      <ProFormSelect
        name="metricName"
        width="lg"
        label={'指标'}
        style={{ marginLeft: 5, marginRight: 5 }}
        placeholder="指标类型"
        options={metricOptions}
        showSearch
        onChange={async (value, option) => {
          const metricDict = keyBy(metrics, 'Name');
          const metric = metricDict[value as string];
          logger.info(`select option metricName => ${value}, DisplayName => ${metric.DisplayName}`);
          const data = await fetchData(metric.Name, appId, platformType);
          logger.info(`fetch data: ${JSON.stringify(data)}`);
          setFormData(data as AlarmNewRulesContainer);
        }}
        rules={[{ required: true, message: '请选择需要设置的告警指标！' }]}
      />
      <ProFormList
        name="rules"
        label={'告警规则'}
        creatorButtonProps={{
          creatorButtonText: '添加或条件',
        }}
        min={1}
        copyIconProps={false}
        itemRender={({ listDom, action }, { index }) => (
          <ProCard
            bordered
            style={{ marginBlockEnd: 8 }}
            title={`或条件分组${index + 1}`}
            extra={action}
            bodyStyle={{ paddingBlockEnd: 0 }}
          >
            {listDom}
          </ProCard>
        )}
        creatorRecord={{
          id: crypto.randomUUID(),
          conditions: [{}],
        }}
        initialValue={[{ conditions: [{}] }]}
      >
        <ProForm.Item isListField style={{ marginBlockEnd: 0 }}>
          <>与条件</>
          <ProFormList
            name="conditions"
            creatorButtonProps={{
              creatorButtonText: '添加与条件',
              icon: false,
              type: 'dashed',
            }}
            min={1}
            copyIconProps={false}
            deleteIconProps={{ tooltipText: '删除' }}
            itemRender={({ listDom, action }) => (
              <div
                style={{
                  display: 'flex',
                  marginInlineEnd: 10,
                }}
              >
                {listDom}
                {action}
              </div>
            )}
          >
            <div
              style={{
                display: 'flex',
                marginInlineEnd: 5,
              }}
            >
              <ProFormSelect
                name="compareVersion"
                width={150}
                placeholder="对比版本"
                style={{ marginRight: 5 }}
                options={compareVersionOptions}
                rules={[{ required: true, message: '请选择对比版本类型' }]}
              />
              <ProFormGroup key="group">
                <ProFormDependency name={['compareVersion']}>
                  {compareVersion => (
                    <ProFormSelect
                      name="valueType"
                      width="xs"
                      style={{ marginLeft: 5, marginRight: 5 }}
                      placeholder="数值类型"
                      options={
                        compareVersion.compareVersion === CompareVersionType.currentVersion
                          ? currentVersionTypeOptions
                          : valueTypeOptions
                      }
                      rules={[{ required: true, message: '请选择数值类型' }]}
                    />
                  )}
                </ProFormDependency>
              </ProFormGroup>

              <ProFormSelect
                name="operator"
                width={100}
                style={{ marginLeft: 5, marginRight: 5 }}
                placeholder="Operator"
                options={operatorOptions}
                rules={[{ required: true, message: '请选择操作符号' }]}
              />
              <ProForm.Item
                style={{ marginLeft: 5, marginRight: 5 }}
                name="value"
                rules={[{ required: true, message: 'Missing value' }]}
              >
                <InputNumber name="value" placeholder="Value" />
              </ProForm.Item>
              <ProFormGroup key="group">
                <ProFormDependency name={['valueType']}>
                  {depValues => (
                    <ProForm.Item style={{ marginLeft: 5, marginRight: 5 }}>
                      {depValues.valueType === ValueType.percentage ? '%' : ''}
                    </ProForm.Item>
                  )}
                </ProFormDependency>
              </ProFormGroup>
            </div>
          </ProFormList>
        </ProForm.Item>
      </ProFormList>
    </ProForm>
  );
};

export default RuleComplexForm;
