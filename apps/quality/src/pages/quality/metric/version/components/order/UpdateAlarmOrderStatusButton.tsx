import { message, Select, Tag } from 'antd';
import React from 'react';
import { OrderStatus } from '@shared/typings/tea/alarm';
import { updateRecordStatus } from '@api/metricsAlarmSystem';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface UpdateAlarmOrderStatusButtonProps {
  status: OrderStatus;
  appId: string;
  platform: PlatformType;
  recordId: string;
  onUpdate?: (state: OrderStatus) => Promise<any>;
}

function orderStatusLabel(status: OrderStatus) {
  const statusTexts = new Map([
    [OrderStatus.New, '未处理'],
    [OrderStatus.Completed, '已处理'],
    [OrderStatus.Pending, '处理中'],
  ]);
  const statusColors = new Map([
    [OrderStatus.New, 'red'],
    [OrderStatus.Completed, 'green'],
    [OrderStatus.Pending, 'orange'],
  ]);
  return <Tag color={statusColors.get(status)}>{statusTexts.get(status)}</Tag>;
}

function orderSelectOptions(status: OrderStatus) {
  return (
    <Select.Option value={status} key={status}>
      {orderStatusLabel(status)}
    </Select.Option>
  );
}

const UpdateAlarmOrderStatusButton: React.FC<UpdateAlarmOrderStatusButtonProps> = ({
  status,
  appId,
  recordId,
  platform,
  onUpdate,
}) => {
  const statusList = [OrderStatus.New, OrderStatus.Completed, OrderStatus.Pending];

  function onSelected(value: OrderStatus) {
    updateRecordStatus({
      data: {
        appId,
        platform,
        recordId,
        status: value,
      },
      headers: {},
    }).then(rsp => {
      if (rsp.code === 0) {
        message.info('更新状态成功');
        onUpdate && onUpdate(value);
      }
    });
  }

  return (
    <Select
      style={{ marginLeft: 5 }}
      placeholder={'更新状态'}
      value={status}
      onChange={(value, option) => {
        onSelected(value);
      }}
    >
      {statusList.map(it => orderSelectOptions(it))}
    </Select>
  );
};

export default UpdateAlarmOrderStatusButton;
