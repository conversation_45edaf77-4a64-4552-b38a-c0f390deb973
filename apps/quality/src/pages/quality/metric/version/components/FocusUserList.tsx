import UserCard from '@/component/UserCard';
import { Avatar } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { getSubscribers } from '@api/metricsAlarmSystem';
import { MetricProps, MultiBusinessProps } from '@shared/typings/tea/alarm';
import { getUsers } from '@/component/UserShow/UserCache';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { User } from '@pa/shared/dist/src/core';

export interface FocusUserListProps {
  maxCount: number;
  refreshAction: number;
}

export const useRefreshFocusUserList = (): [number, () => void] => {
  const [refreshKey, setRefreshKey] = useState(0);
  const handleRefresh = useCallback(() => {
    setRefreshKey(prevKey => prevKey + 1);
  }, []);
  return [refreshKey, handleRefresh];
};

export const FocusUserList: React.FC<FocusUserListProps & MultiBusinessProps & MetricProps> = ({
  maxCount,
  appId,
  metricId,
  platformType,
  owner,
  refreshAction,
}) => {
  const [users, setUsers] = useState<User[]>();
  useEffect(() => {
    getSubscribers({ data: { metric_name: metricId, appId, platformType } }).then(async subscribers => {
      if (!subscribers) {
        owner && setUsers(await getUsers([owner]));
        return;
      }
      if (owner) {
        subscribers.push(owner);
      }
      const allUser = Array.from(new Set(subscribers));
      logger.info(`subscribers: ${JSON.stringify(allUser)}`);
      setUsers(await getUsers(allUser));
    });
  }, [refreshAction]);
  return (
    <Avatar.Group size={{ xs: 20 }} maxCount={maxCount} maxStyle={{ color: '#ffffff', backgroundColor: '#006bf6' }}>
      {users?.map(user => (
        <>
          <UserCard
            email={`${user.email}`}
            simpleUserData={{
              // name: user.name_cn,
              avatarUrl: (user.avatar as any)?.avatar_72, // avatar_url,
            }}
            hideName={true}
            triggerType="hover"
          />
        </>
      ))}
    </Avatar.Group>
  );
};
