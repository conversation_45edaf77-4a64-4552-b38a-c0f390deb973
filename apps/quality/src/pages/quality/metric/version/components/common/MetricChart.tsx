import React from 'react';
import { CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

export interface MetricChartProps {
  data: any[];
}

// 生成一个色相值范围从0到360的函数
const getHue = (index: number, total: number) => (index / total) * 360;

// 生成HSL颜色
const getColor = (index: number, total: number) => `hsl(${getHue(index, total)}, 70%, 50%)`;

const MetricChart: React.FC<MetricChartProps> = ({ data }) => {
  const keys = Array.from(
    new Set(data?.flatMap(Object.keys)?.filter(it => it !== 'time' && it !== 'timeStamp' && it !== 't')),
  );
  const totalKeys = keys?.length ?? 1;
  return (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="time" />
        <YAxis
          tickFormatter={value => `${value}`} // 格式化刻度值
          // ticks={[0, 100, 200, 300, 400]} // 指定显示的刻度
        />
        <Tooltip />
        <Legend />
        {keys.map((key, index) => (
          <Line key={key} type="monotone" dataKey={key} stroke={getColor(index, totalKeys)} activeDot={{ r: 8 }} />
        ))}
      </LineChart>
    </ResponsiveContainer>
  );
};

export default MetricChart;
