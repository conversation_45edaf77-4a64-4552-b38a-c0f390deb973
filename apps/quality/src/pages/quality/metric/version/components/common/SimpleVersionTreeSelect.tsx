import React, { useState } from 'react';
import { TreeSelect } from 'antd';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import TreeValue from '@/component/treeValue';
import { keyBy } from 'lodash';

export interface SimpleVersionTreeSelectProps {
  versions: TreeValue[];
  onChange?: (selectValue: string[]) => void;
}
export const SimpleVersionTreeSelect: React.FC<SimpleVersionTreeSelectProps> = ({ versions, onChange }) => {
  const [value, setValue] = useState<string[]>();
  const map = keyBy(versions, v => v.value);

  const handleChange = (newValue: string[]) => {
    logger.debug('[SimpleVersionTreeSelect] onChange ', newValue);
    setValue(newValue);
    if (onChange) {
      // 树状结构还原
      const verCodes: string[] = [];
      newValue.forEach(v => {
        if (!map[v]) {
          verCodes.push(v);
        }
        verCodes.push(...map[v].children.map(it => it.value));
      });
      onChange(verCodes);
    }
  };

  const tProps = {
    treeData: versions,
    value,
    onChange: handleChange,
    treeCheckable: true,
    showCheckedStrategy: TreeSelect.SHOW_PARENT,
    placeholder: '请选择版本',
    style: {
      width: 300,
    },
  };

  return <TreeSelect {...tProps} />;
};

export default SimpleVersionTreeSelect;
