import React, { useState } from 'react';
import { Button, Form, InputNumber, Select } from 'antd';
import { AlarmCondition, AlarmRule, CompareVersionType, ValueType } from '@shared/typings/tea/RuleTypes';
import { CloseOutlined } from '@ant-design/icons';
import { DBTeaMetric } from '../../../../../../../api/model/TeaMetricTable';
import { keyBy } from 'lodash';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';

const { Option } = Select;

interface RuleFormProps {
  rule?: AlarmRule;
  options: DBTeaMetric[];
  onSave: (rule: AlarmRule) => void;
  onCancel: () => void;
}

interface FormItemState {
  valueType: ValueType;
  compareVersionType: CompareVersionType;
}

interface FormState {
  [key: string]: FormItemState;
}

const RuleForm: React.FC<RuleFormProps> = ({ rule, options, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [formState, setFormState] = useState<FormState>({});
  const optionsMap = keyBy(options, 'Name');
  const [{ info }] = useModel(AppSettingModule);

  const handleSave = () => {
    form.validateFields().then(values => {
      const newRule: AlarmRule = {
        name: rule?.name ?? crypto.randomUUID(),
        displayName: rule?.displayName,
        appId: info.businessInfo.app_id.toString(),
        platformType: info.platform,
        ...values,
        conditions: values.conditions.map((condition: AlarmCondition) => {
          console.log(`RuleForm handleSave condition => ${JSON.stringify(condition)}}`);
          return {
            ...condition,
            conditionName: condition?.conditionName ?? crypto.randomUUID(),
            displayName: condition.displayName,
            value: condition.value,
          };
        }),
        owner: optionsMap[values.metricName]?.POC,
      };
      console.log(`RuleForm handleSave values => ${JSON.stringify(values)},
			 name => ${values.metricName}, rule => {${JSON.stringify(rule)}},
			 new rule => ${JSON.stringify(newRule)}`);
      onSave(newRule);
    });
  };

  const handleChange = (key: string, newValue: ValueType) => {
    console.log(`handleChange key => ${key}`);
    setFormState({
      ...formState,
      [key]: { ...formState[key], valueType: newValue },
    });
  };

  return (
    <Form form={form} layout="vertical" initialValues={rule} style={{ margin: '0 auto' }}>
      <Form.Item
        name="metricName"
        label="指标"
        rules={[{ required: true, message: '请选择要配置的告警规则!' }]}
        style={{ marginBottom: 0 }}
      >
        <Select
          onSelect={(_, option) => {
            form.setFieldValue('displayName', `${option.children}`);
            form.setFieldValue('owner', `${rule?.owner}`);
          }}
        >
          {options.map(option => (
            <Option key={option.Name} value={option.Name}>
              {option.DisplayName}
            </Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item name="displayName" noStyle />
      <Form.Item name="owner" noStyle />
      <Form.Item name="rules" style={{ marginTop: 10, marginBottom: 10 }}>
        告警规则
      </Form.Item>
      <Form.List name="conditions">
        {(fields, { add, remove }) => (
          <>
            {fields.map(field => (
              <div key={field.key} style={{ display: 'flex', marginBottom: 0, marginTop: 0, marginBlockEnd: 0 }}>
                <Form.Item style={{ minWidth: 100, marginRight: 5 }} name={[field.name, 'compareVersion']}>
                  <Select
                    placeholder="对比版本"
                    style={{ width: 160, marginRight: 5 }}
                    onChange={(value, _) => {
                      const versionSelectKey = String(field.name);
                      setFormState({
                        ...formState,
                        [versionSelectKey]: {
                          ...formState[versionSelectKey],
                          compareVersionType: value,
                        },
                      });
                    }}
                  >
                    <Option value={CompareVersionType.currentVersion}>当前版本</Option>
                    <Option value={CompareVersionType.lastSmallVersion}>上一个灰度小版本</Option>
                    <Option value={CompareVersionType.lastBigVersion}>上一周期灰度版本</Option>
                  </Select>
                </Form.Item>
                <Form.Item style={{ minWidth: 100, marginRight: 5 }} name={[field.name, 'valueType']}>
                  <Select
                    placeholder="数值类型"
                    onChange={(value, _) => {
                      handleChange(String(field.name), value);
                    }}
                  >
                    <Option value={ValueType.absolute}>绝对值</Option>
                    {formState[field.name] &&
                      formState[field.name].compareVersionType !== CompareVersionType.currentVersion && (
                        <Option value={ValueType.percentage}>百分比</Option>
                      )}
                  </Select>
                </Form.Item>
                <Form.Item
                  style={{ marginRight: 5 }}
                  {...field}
                  name={[field.name, 'operator']}
                  rules={[{ required: true, message: 'Missing operator' }]}
                >
                  <Select placeholder="Operator" style={{ minWidth: 50 }}>
                    <Option value="==">{'='}</Option>
                    <Option value="!=">{'≠'}</Option>
                    <Option value=">=">{'≥'}</Option>
                    <Option value="<">{'<'}</Option>
                    <Option value="<=">{'≤'}</Option>
                    <Option value=">">{'>'}</Option>
                  </Select>
                </Form.Item>
                <Form.Item
                  {...field}
                  style={{ marginRight: 5 }}
                  name={[field.name, 'value']}
                  rules={[{ required: true, message: 'Missing value' }]}
                >
                  <InputNumber placeholder="Value" />
                </Form.Item>
                {formState[field.name] && formState[field.name].valueType === ValueType.percentage && (
                  <Form.Item style={{ marginRight: 5 }}>%</Form.Item>
                )}
                <Button type="dashed" onClick={() => remove(field.name)} icon={<CloseOutlined />} />
              </div>
            ))}
            <Button type="dashed" onClick={() => add()}>
              添加与条件
            </Button>
          </>
        )}
      </Form.List>
      <Form.Item style={{ marginTop: 10 }}>
        <Button
          type="primary"
          onClick={() => {
            handleSave();
            // form.resetFields();
          }}
        >
          保存
        </Button>
        <Button onClick={onCancel} style={{ marginLeft: 5 }}>
          取消
        </Button>
      </Form.Item>
    </Form>
  );
};

export default RuleForm;
