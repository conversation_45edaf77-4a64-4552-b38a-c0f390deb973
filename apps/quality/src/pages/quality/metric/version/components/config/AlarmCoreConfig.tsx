import React, { useCallback, useEffect, useRef, useState } from 'react';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import { Avatar, Button, message, Switch, TablePaginationConfig, Tooltip } from 'antd';
import UserShow from '@/component/UserShow';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import { get_enum_values } from '@shared/utils/tools';
import { coreMonitorList, updateMonitorStatue } from '@api/metricsAlarmSystem';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import UserSettingModule from '@/model/userSettingModel';
import AlarmFollowButton from '@/pages/quality/metric/version/components/AlarmFollowButton';
import { AntDesignOutlined, UserOutlined } from '@ant-design/icons';
import UserCard from '@/component/UserCard';
import { FocusUserList, useRefreshFocusUserList } from '@/pages/quality/metric/version/components/FocusUserList';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { useLocalStorage } from '@/pages/quality/metric/version/utils/StoragesUtils';
import type { FilterValue } from 'antd/es/table/interface';

export interface AlarmCoreConfigProps {}

type AlarmCoreConfigItem = {
  metricName: string;
  business: string;
  businessType?: Business;
  displayName: string;
  owner: string;
  status: boolean;
};

const AlarmCoreConfig: React.FC = () => {
  const [data, setData] = useState<AlarmCoreConfigItem[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const appId = appSettingState.info.businessInfo.app_id.toString();
  const platformType = appSettingState.info.businessInfo.platform;
  const [refreshAction, refreshFocusUserList] = useRefreshFocusUserList();
  const [pageSize, setPageSize] = useState(10);
  const [businessFilters, setBusinessFilters] = useLocalStorage<string[]>(`${appId}_AlarmCoreConfigBusinessFilters`);

  const handleTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>) => {
    setBusinessFilters(filters.businessType ? filters.businessType.map(it => it.toString()) : []);
  };
  useEffect(() => {
    coreMonitorList({
      data: {
        appId: appSettingState.info.businessInfo.app_id.toString(),
        platform: appSettingState.info.platform,
      },
    }).then(rsp => {
      setData(rsp.data ?? []);
    });
  }, [appSettingState.info.businessInfo.app_id, appSettingState.info.platform]);
  async function changeCoreMetricMonitorStats(record: AlarmCoreConfigItem) {
    return updateMonitorStatue({
      data: {
        appId: appSettingState.info.businessInfo.app_id.toString(),
        platform: appSettingState.info.platform,
        metricName: record.metricName,
        status: !record.status,
      },
    }).then(r => {
      setData(data.map(item => (item.metricName === record.metricName ? { ...item, status: !item.status } : item)));
    });
  }
  const handleStatusToggle = (record: AlarmCoreConfigItem) => {
    changeCoreMetricMonitorStats(record).then(r => {
      logger.debug('handleButtonClick');
      message.info(`${!record.status ? '加入' : '移除'}版本核心指标准出成功!`);
    });
  };

  const columns: ProColumns<AlarmCoreConfigItem>[] = [
    {
      title: '指标名称',
      dataIndex: 'displayName',
      key: 'displayName',
    },
    {
      title: '业务',
      dataIndex: 'businessType',
      key: 'businessType',
      render: (_, { businessType }) => <> {businessType !== undefined && BusinessName[businessType]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      filteredValue: businessFilters,
      onFilter: (filterBusiness, config) => config.businessType === filterBusiness,
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (_, row) => <UserShow email={row.owner} />,
    },
    {
      title: '关注人列表',
      dataIndex: 'subscribers',
      key: 'subscribers',
      render: (_, row) => (
        <FocusUserList
          appId={appId}
          platformType={platformType}
          owner={row.owner}
          metricId={row.metricName}
          maxCount={3}
          refreshAction={refreshAction}
        />
      ),
    },
    {
      title: '版本准出监控',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => <Switch checked={record.status} onChange={() => handleStatusToggle(record)} />,
    },
    {
      title: '操作',
      dataIndex: 'metricName',
      key: 'action',
      render: (_, record) => (
        <>
          <AlarmFollowButton
            metricId={record.metricName}
            onStatusChange={() => {
              refreshFocusUserList();
            }}
          />
          <Button style={{ marginLeft: 5 }} onClick={() => handleStatusToggle(record)}>
            {record.status ? '移除监控' : '加入监控'}
          </Button>
        </>
      ),
    },
  ];

  return (
    <ProTable<AlarmCoreConfigItem>
      columns={columns}
      dataSource={data}
      options={false}
      rowKey="metricName"
      search={false}
      onChange={handleTableChange}
      pagination={{
        pageSize,
        onShowSizeChange: (c, size) => {
          setPageSize(size);
        },
        pageSizeOptions: [10, 20, 50, 100, 150, 200],
        showSizeChanger: true,
      }}
      toolBarRender={() => []}
    />
  );
};

export default AlarmCoreConfig;
