import React, { useEffect, useState } from 'react';
import {
  AlarmNewCondition,
  AlarmNewRule,
  AlarmNewRulesContainer,
  AlarmRule,
  CompareVersionType,
  ValueType,
} from '@shared/typings/tea/RuleTypes';
import { convertToFormat } from '@/pages/quality/metric/version/model/rules/RuleConverter';
import { Card, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import SyntaxHighlighter from 'react-syntax-highlighter';
import copy from 'copy-to-clipboard';
import { PlatformType } from '@pa/shared/dist/src/core';

export interface RuleContainer {
  rules: AlarmRule[];
  newRuleContainer?: AlarmNewRulesContainer;
}

// const generateCode = (_rules: AlarmRule[]) => JSON.stringify(_rules, null, 2);
const generateCode = (_rules: AlarmRule[]) => convertToFormat(_rules);
const copyText = (text: string) => {
  copy(text);
  message.success(`复制成功 ${text}`).then();
};

const getPlatform = (newRuleContainer?: AlarmNewRulesContainer): string => {
  const platformType = newRuleContainer?.platformType;
  if (platformType === PlatformType.Android) {
    return 'android';
  }
  if (platformType === PlatformType.iOS) {
    return 'ios';
  }
  return 'unknown';
};

const field = (compareVersionType?: CompareVersionType): string => `${compareVersionType}.value`;

const parseCondition = (condition?: AlarmNewCondition): string => {
  if (condition?.compareVersion === CompareVersionType.currentVersion) {
    return `${field(condition?.compareVersion)} ${condition.operator} ${condition.value}`;
  }
  if (condition?.valueType === ValueType.absolute) {
    return `(${field(CompareVersionType.currentVersion)} - ${field(condition?.compareVersion)}) ${condition.operator} ${condition.value}`;
  }
  if (condition?.valueType === ValueType.percentage) {
    return `(${field(CompareVersionType.currentVersion)} - ${field(condition?.compareVersion)}) / ${field(condition.compareVersion)} ${condition.operator} ${condition.value}%`;
  }
  return '';
};

const parseRule = (rule?: AlarmNewRule) => rule?.conditions.map(parseCondition);

const parseRules = (rulesContainer?: AlarmNewRulesContainer) => rulesContainer?.rules.map(parseRule);

const format = (rules: (string[] | undefined)[] | undefined, currentIndex: number) => {
  let accNo = currentIndex + 1;
  let t_rule = '';
  const c_rules = rules
    ?.map((rule, ruleIndex) => {
      let t_condition = '';
      const c_conditions = rule
        ?.map((condition, i) => {
          const detailCode = `\n    c${accNo} = ${condition}`;
          t_condition += `${i > 0 ? ' and ' : ''}c${accNo}`;
          accNo += 1;
          return detailCode;
        })
        .join('');
      t_rule += `${ruleIndex > 0 ? ' or ' : ''}${t_condition}`;
      return c_conditions;
    })
    .join('');
  return [t_rule, c_rules];
};

const RuleCodeGenerator: React.FC<RuleContainer> = ({ rules, newRuleContainer }) => {
  const [pythonCode, setPythonCode] = useState<string | undefined>(undefined);
  useEffect(() => {
    const rulesCode = format(parseRules(newRuleContainer), 1);
    let code = `def filter(req):`;
    let condition = '';
    code += `\n    c1 = req.get('device_platform') is not None and req['device_platform'].lower() == u'${getPlatform(newRuleContainer)}'`;
    // code += `\n    c2 = 'version_code' in req and version2int(req.get('version_code')) >= 1600`;
    code += `  ${rulesCode[1]}`;
    condition += `c1 and (${rulesCode[0]})`;
    code += `\n    return ${condition}`;
    setPythonCode(code);
  }, [newRuleContainer]);

  return (
    <>
      <>{generateCode(rules)}</>
      <Card bordered={true} style={{ width: 1200, marginTop: 10 }} styles={{ body: { padding: 0 } }}>
        {pythonCode && (
          <CopyOutlined
            onClick={() => copyText(pythonCode)}
            style={{
              color: '#1677ff',
              position: 'absolute',
              top: 10,
              right: 10,
            }}
          />
        )}
        <SyntaxHighlighter
          language="python"
          wrapLines={true}
          wrapLongLines={true}
          customStyle={{
            padding: 16,
            margin: 0,
            fontSize: 13,
          }}
        >
          {`${pythonCode}`}
        </SyntaxHighlighter>
      </Card>
    </>
  );
};

export default RuleCodeGenerator;
