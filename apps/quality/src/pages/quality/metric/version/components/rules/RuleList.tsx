import React, { useEffect } from 'react';
import { Table, But<PERSON>, Popconfirm } from 'antd';
import {
  CompareVersionType,
  AlarmRule,
  ValueType,
  AlarmNewRulesContainer,
  AlarmNewRule,
} from '@shared/typings/tea/RuleTypes';
import UserShow from '@/component/UserShow';
import { testDetectMetricAlarmRisk } from '@api/metricsAlarmSystem';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';

interface RuleListProps {
  rules: AlarmRule[];
  newRules?: AlarmNewRulesContainer[];
  onEdit: (rule: AlarmRule) => void;
  onNewEdit?: (rule: AlarmNewRulesContainer) => void;
  onDelete: (rule: AlarmRule) => void;
  onNewDelete?: (rule: AlarmNewRulesContainer) => void;
  onShowDetail?: (rule: AlarmNewRulesContainer) => void;
}

function getCompareVersionShowText(compareVersionType: CompareVersionType): string {
  const showTexts = new Map<CompareVersionType, string>([
    [CompareVersionType.currentVersion, '当前版本'],
    [CompareVersionType.lastSmallVersion, '对比上一个灰度小版本'],
    [CompareVersionType.lastBigVersion, '对比上一个大版本灰度'],
  ]);
  return showTexts.get(compareVersionType) as string;
}

function getValueTypeShowText(valueType: ValueType): string {
  const textMap = new Map([
    [ValueType.absolute, '绝对值'],
    [ValueType.percentage, '百分比'],
  ]);
  return textMap.get(valueType) as string;
}

const RuleList: React.FC<RuleListProps> = ({
  rules,
  newRules,
  onEdit,
  onDelete,
  onNewEdit,
  onNewDelete,
  onShowDetail,
}) => {
  const [loginInfo] = useModel(UserSettingModule);

  const columns = [
    {
      title: '指标',
      dataIndex: 'displayName',
      key: 'displayName',
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: string) => <UserShow email={owner ?? ''} />,
    },
    {
      title: '规则',
      dataIndex: 'rules',
      key: 'rules',
      render: (alarmNewRules: AlarmNewRule[]) => (
        <>
          {alarmNewRules.map((rule, index) => (
            <>
              {`规则${index + 1}:`}
              {<br />}
              {rule.conditions.map((condition, conditionIndex) => (
                <>
                  {conditionIndex > 0 ? '且 ' : ''}
                  {getCompareVersionShowText(condition.compareVersion)}
                  {getValueTypeShowText(condition.valueType)}
                  {'变化'} {condition.operator} {condition.value}
                  {condition.valueType === ValueType.percentage ? '%' : ''}
                  {<br />}
                </>
              ))}
            </>
          ))}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (text: any, record: AlarmNewRulesContainer) => (
        <span>
          <Button onClick={() => onNewEdit && onNewEdit(record)}>编辑</Button>
          <Button style={{ marginLeft: 5 }} onClick={() => onShowDetail && onShowDetail(record)}>
            查看代码
          </Button>
          <Popconfirm
            title="确定要删除当前规则吗?"
            onConfirm={() => onNewDelete && onNewDelete(record)}
            okText={'确定'}
            cancelText={'取消'}
          >
            <Button style={{ marginLeft: 5 }} type="primary" danger>
              删除
            </Button>
          </Popconfirm>
          <Button
            type={'primary'}
            danger
            style={{ marginLeft: 5 }}
            onClick={() => {
              testDetectMetricAlarmRisk({
                data: {
                  appId: record.appId,
                  platform: record.platformType,
                  email: loginInfo.info.email,
                  metricName: record.metricName,
                },
              }).then();
            }}
          >
            测试规则
          </Button>
        </span>
      ),
    },
  ];

  return <Table rowKey="metrics" dataSource={newRules} columns={columns} />;
};

export default RuleList;
