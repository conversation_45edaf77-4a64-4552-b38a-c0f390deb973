import React, { useEffect, useRef, useState } from 'react';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import UserSettingModule from '@/model/userSettingModel';
import InviteGroupButton from '@/pages/quality/metric/version/components/InviteGroupButton';
import UserShow from '@/component/UserShow';
import { Button, Drawer, Select, Space, TablePaginationConfig, Tag } from 'antd';
import UpdateAlarmOrderStatusButton from '@/pages/quality/metric/version/components/order/UpdateAlarmOrderStatusButton';
import { allRecords, getRecord, searchRecords } from '@api/metricsAlarmSystem';
import { OrderStatus, OrderStatusText } from '@shared/typings/tea/alarm';
import AlarmRecordDetail from '@/pages/quality/metric/version/components/order/AlarmRecordDetail';
import { get_enum_values } from '@shared/utils/tools';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import { logger } from '@/pages/quality/metric/version/utils/Logger';
import { keyBy, map, toNumber } from 'lodash';
import { useLocalStorage } from '@/pages/quality/metric/version/utils/StoragesUtils';
import type { FilterValue } from 'antd/es/table/interface';
import { formatAlarmTime, QualityAlarmOrderRecord } from '@shared/metriec/order/QualityAlarmOrderRecord';
import { AlarmRecordParser } from '@shared/metriec/AlarmRecordParser';

export interface QualityAlarmOrderProps {
  hiddenSearch?: boolean;
  enablePagination?: boolean;
  metricIds?: string[];
}

enum TAB_STATE {
  RECORD = 'record',
}

const buildBusinessOptions = (enumData: typeof BusinessName) =>
  keyBy(
    map(enumData, (value, key) => ({
      text: value,
      type: key,
    })),
    item => item.type,
  );

const QualityAlarmOrderRecordList: React.FC<QualityAlarmOrderProps> = ({
  hiddenSearch,
  enablePagination,
  metricIds,
}) => {
  const [appSettingState] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const [currentTab, setCurrentTab] = useState(TAB_STATE.RECORD.valueOf());
  const actionRef = useRef<ActionType>();
  const [pageSize, setPageSize] = useState(40);
  const appId = appSettingState.info.id.toString();
  const platformType = appSettingState.info.platform;
  const [alarmOrderRecords, setAlarmOrderRecords] = useState<QualityAlarmOrderRecord[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectRecord, setSelectRecord] = useState<QualityAlarmOrderRecord>();
  const [businessFilters, setBusinessFilters] = useLocalStorage<string[]>(
    `${appId}_QualityAlarmOrderRecordListBusinessFilters`,
  );

  const handleTableChange = (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>) => {
    setBusinessFilters(filters.business ? filters.business.map(it => it.toString()) : []);
  };
  async function requestOrderRecords(params: any) {
    return searchRecords({
      data: {
        appId,
        platform: platformType,
        ...params,
        metricIds,
      },
    }).then(rsp => {
      if (rsp.code === 0) {
        return AlarmRecordParser.parse(rsp.data);
      }
    });
  }
  function onClickDetail(record: QualityAlarmOrderRecord) {
    setSelectRecord({ ...record });
    setIsDrawerOpen(true);
  }

  const columns: ProColumns<QualityAlarmOrderRecord>[] = [
    {
      title: '告警指标',
      key: 'displayName',
      dataIndex: 'displayName',
      width: 220,
      fixed: 'left',
      ellipsis: true,
      disable: true,
      order: 1,
      render: (_, row) => (
        <a
          onClick={() => {
            window.open(row.teaUrl);
          }}
        >
          {row.displayName}
        </a>
      ),
    },
    {
      title: '告警时间',
      key: 'timestamp',
      dataIndex: 'timestamp',
      width: 220,
      fixed: 'left',
      valueType: 'dateTimeRange',
      ellipsis: true,
      search: false,
      disable: true,
      order: 2,
      render: (_, row) => <>{formatAlarmTime(row.timestamp)}</>,
    },
    {
      title: '负责人',
      key: 'owner',
      dataIndex: 'owner',
      width: 120,
      fixed: 'left',
      ellipsis: true,
      disable: true,
      order: 3,
      render: (_, row) => <UserShow email={row.owner} />,
    },
    {
      title: '业务',
      key: 'business',
      dataIndex: 'business',
      width: 90,
      fixed: 'left',
      ellipsis: true,
      disable: true,
      order: 4,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      filteredValue: businessFilters,
      onFilter: (filterBusiness, records) => records.business === filterBusiness,
      valueType: 'select',
      valueEnum: buildBusinessOptions(BusinessName),
      render: (_, row) => `${row.businessName ?? BusinessName[row.business]}`,
    },
    {
      title: '版本',
      dataIndex: 'versionCode',
      width: 90,
      align: 'center',
      order: 5,
      filters: [{ text: '143000100', value: '143000100' }],
      onFilter: (value, record) => value === record.versionCode?.toString(),
    },
    {
      title: '处理状态',
      hideInSearch: true,
      disable: true,
      dataIndex: 'orderStatus',
      width: 150,
      order: 6,
      align: 'center',
      filters: [OrderStatus.New, OrderStatus.Pending, OrderStatus.Completed].map(it => ({
        text: OrderStatusText[it],
        value: it,
      })),
      onFilter: (value, record) => value === record.orderStatus?.toString(),
      render: (value, record) => (
        <UpdateAlarmOrderStatusButton
          status={record.orderStatus}
          recordId={record.recordId}
          appId={appId}
          platform={platformType}
          onUpdate={async state => {
            const rsp = await getRecord({
              data: {
                appId,
                platformType,
                metricName: record.metricName,
                recordId: record.recordId,
              },
            });

            setAlarmOrderRecords(
              alarmOrderRecords.map(r => {
                if (r.recordId !== record.recordId) {
                  return r;
                }
                return rsp.data as QualityAlarmOrderRecord;
                // r.orderStatus = state;
                // return r;
              }),
            );
          }}
        />
      ),
    },
    {
      title: '操作类型',
      key: 'actions',
      width: 400,
      fixed: 'left',
      order: 7,
      ellipsis: true,
      hideInSearch: true,
      search: false,
      disable: true,
      render: (_, row) => (
        <>
          <InviteGroupButton metricName={row.metricName} versionCode={row.versionCode} />
          <Button
            type={'primary'}
            style={{ marginLeft: 8, marginRight: 8 }}
            onClick={v => {
              onClickDetail(row);
            }}
          >
            查看详情
          </Button>
        </>
      ),
    },
  ];
  return (
    <>
      <ProTable<QualityAlarmOrderRecord>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        params={{ currentTab, appId }}
        request={async (params, sort, filter) => {
          logger.info(`requestRecordList`, JSON.stringify(params), JSON.stringify(sort), JSON.stringify(filter));
          if (params.business) {
            params.business = toNumber(params.business);
          }
          const resp = await requestOrderRecords(params);
          return resp || [];
        }}
        onLoad={(dataSource: QualityAlarmOrderRecord[]) => {
          setAlarmOrderRecords(dataSource);
        }}
        onChange={handleTableChange}
        dataSource={alarmOrderRecords}
        editable={{
          type: 'multiple',
        }}
        columnsState={{
          persistenceKey: 'quality_alarm_order_record_list',
          persistenceType: 'localStorage',
          value: {
            actions: { order: 7 },
          },
        }}
        scroll={{
          y: '100%',
          scrollToFirstRowOnChange: true,
        }}
        options={false}
        search={
          hiddenSearch !== false && {
            filterType: 'query',
            labelWidth: 'auto',
            collapsed: false,
            span: 8,
          }
        }
        pagination={
          enablePagination !== false && {
            pageSize,
            pageSizeOptions: [10, 20, 40, 60, 80, 100, 500],
            showSizeChanger: true,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
            },
          }
        }
        toolbar={{
          multipleLine: true,
          tabs: {
            activeKey: currentTab,
            items: [
              {
                key: 'record',
                tab: '告警记录',
              },
            ],
            onChange: key => {
              setCurrentTab(key);
            },
          },
        }}
      />
      <Drawer
        size="large"
        title={`【${selectRecord?.displayName}】告警详情(${selectRecord?.recordId}):`}
        footer={
          <Space direction="horizontal" size="large" style={{ float: 'right' }}>
            {selectRecord && (
              <InviteGroupButton metricName={selectRecord?.metricName} versionCode={selectRecord?.versionCode} />
            )}
          </Space>
        }
        open={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false);
          setSelectRecord(undefined);
        }}
        width={1000}
      >
        {<AlarmRecordDetail record={selectRecord} />}
      </Drawer>
    </>
  );
};

export default QualityAlarmOrderRecordList;
