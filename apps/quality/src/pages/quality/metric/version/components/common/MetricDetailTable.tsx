import React, { useState } from 'react';
import { Table, TableProps } from 'antd';
import 'antd/dist/reset.css';
import dayjs from 'dayjs';
import { MetricDataType } from '@shared/typings/tea/alarm';
import UserShow from '@/component/UserShow';
import { Business, BusinessName } from '@shared/typings/tea/metric';
import { get_enum_values } from '@shared/utils/tools'; // Import Ant Design styles

export interface MetricDetailDisplayType extends MetricDataType {
  timeStamp: number;
}

type Props = {
  data?: MetricDetailDisplayType[];
};

const MetricDetailTable: React.FC<Props> = ({ data }) => {
  // Define columns for the table
  const columns: TableProps<MetricDetailDisplayType>['columns'] = [
    {
      title: '指标名称',
      dataIndex: ['name', 'teaUrl'],
      key: 'name',
      render: (_, { name, teaUrl }) => (
        <div style={{ display: 'flex' }}>
          <a
            onClick={() => {
              window.open(teaUrl);
            }}
          >
            {name}
          </a>
        </div>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (_, { owner }) => <UserShow email={owner ?? ''} />,
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      render: (_, { business }) => <>{BusinessName[business ?? Business.MAIN_FRAMEWORK]}</>,
      filters: get_enum_values(Business).map(it => ({
        text: BusinessName[it],
        value: it,
      })),
      onFilter: (filterBusiness, performanceData) => performanceData.business === filterBusiness,
    },
    {
      title: '版本号',
      key: 'versionCode',
      dataIndex: 'versionCode',
    },
    {
      title: '指标',
      dataIndex: 'value',
      key: 'value',
      render: (_, { value }) => <>{value.toFixed(6)}</>,
    },
    {
      title: '时间',
      dataIndex: 'timeStamp',
      key: 'timeStamp',
      render: (_, { timeStamp }) => <>{dayjs.unix(timeStamp).format('YYYY-MM-DD')}</>,
    },
  ];

  return <Table columns={columns} dataSource={data} />;
};

export default MetricDetailTable;
