import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Mo<PERSON>, Select } from 'antd';
import { AlarmCondition, AlarmNewRule, AlarmNewRulesContainer, AlarmRule } from '@shared/typings/tea/RuleTypes';
import RuleList from '@/pages/quality/metric/version/components/rules/RuleList';
import RuleForm from '@/pages/quality/metric/version/components/rules/RuleForm';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { DBTeaMetric } from '../../../../../../../api/model/TeaMetricTable';
import RuleCodeGenerator from '@/pages/quality/metric/version/components/rules/RuleCodeGenerator';
import { allNewRules, removeNewRule, removeRule, testAllAlarmRisk, updateNewRule } from '@api/metricsAlarmSystem';
import { getPerformanceDataTypeWithAppInfo } from '@/pages/quality/metric/version/model/QualityDataClient';
import UserSettingModule from '@/model/userSettingModel';
import RuleComplexForm from '@/pages/quality/metric/version/components/rules/RuleComplexForm';
import { logger } from '@/pages/quality/metric/version/utils/Logger';

const RulesConfig: React.FC = () => {
  const [teaMetrics, setTeaMetrics] = useState<DBTeaMetric[]>([]);
  const [rules, setRules] = useState<AlarmRule[]>([]);
  const [newRules, setNewRules] = useState<AlarmNewRulesContainer[]>([]);
  const [currentNewRule, setCurrentNewRule] = useState<AlarmNewRulesContainer | undefined>(undefined);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showRule, setShowRule] = useState<AlarmNewRulesContainer>();
  const [{ info }] = useModel(AppSettingModule);
  const [loginInfo] = useModel(UserSettingModule);
  const appId = info.businessInfo.app_id.toString();
  const platformType = info.platform;

  useEffect(() => {
    Promise.all([
      getPerformanceDataTypeWithAppInfo(info),
      allNewRules({
        data: {
          appId,
          platformType,
        },
      }),
    ]).then(([metrics, _rules]) => {
      setTeaMetrics(metrics);
      setNewRules(_rules.data as AlarmNewRulesContainer[]);
      logger.info(`RulesConfig rules => ${JSON.stringify(_rules)}`);
    });
  }, [info]);

  // useEffect(() => {
  // 	convert(rules);
  // }, [rules]);
  const handleAdd = () => {
    setIsModalVisible(true);
  };

  const handleEdit = (rule: AlarmRule) => {
    logger.info(`handleEdit => ${JSON.stringify(rule)}`);
    setIsModalVisible(true);
  };

  const handleNewRuleEdit = (rule: AlarmNewRulesContainer) => {
    logger.info(`handleNewRuleEdit => ${JSON.stringify(rule)}`);
    setCurrentNewRule(rule);
    setIsModalVisible(true);
  };
  const handleClickDetail = (rule: AlarmNewRulesContainer) => {
    setShowRule(rule);
  };

  const handleDelete = (rule: AlarmRule) => {
    logger.info(`handleDelete rule => ${JSON.stringify(rule)}`);
    removeRule({
      data: {
        appId: info.businessInfo.app_id.toString(),
        platformType: info.platform,
        name: rule.name,
      },
    }).then(() => {
      setRules(rules.filter(r => r !== rule));
    });
  };

  const handleNewRuleDelete = (rule: AlarmNewRulesContainer) => {
    logger.info(`handleDelete rule => ${JSON.stringify(rule)}`);
    removeNewRule({
      data: {
        appId: rule.appId,
        platformType: rule.platformType,
        metricName: rule.metricName,
      },
    }).then(() => {
      setNewRules(newRules.filter(r => r.metricName !== rule.metricName));
    });
  };

  const handleSaveComplexRule = (inRules: AlarmNewRulesContainer) => {
    console.log(`handleSaveComplexRule => ${JSON.stringify(inRules)}`);
    updateNewRule({ data: inRules }).then(() => {
      setIsModalVisible(false);
      console.log(`handleSave => ${JSON.stringify(inRules)}`);
      setNewRules([...newRules.filter(r => r.metricName !== inRules.metricName), inRules]);
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" style={{ marginRight: 10 }} onClick={handleAdd}>
          添加告警规则
        </Button>
        {[''].includes(loginInfo.info.email) && (
          <Button
            type="primary"
            danger
            onClick={() => {
              testAllAlarmRisk({
                data: {
                  appId,
                  platform: platformType,
                  email: loginInfo.info.email,
                },
              }).then();
            }}
          >
            测试所有规则
          </Button>
        )}
      </div>
      <RuleList
        rules={rules}
        onEdit={handleEdit}
        onDelete={handleDelete}
        newRules={newRules}
        onNewDelete={handleNewRuleDelete}
        onNewEdit={handleNewRuleEdit}
        onShowDetail={handleClickDetail}
      />
      <Modal
        title={currentNewRule ? '编辑规则' : '添加规则'}
        visible={isModalVisible}
        footer={null}
        onCancel={handleCancel}
        width={600}
      >
        <RuleComplexForm
          metrics={teaMetrics}
          appId={appId}
          currentRule={currentNewRule}
          platformType={platformType}
          onSave={handleSaveComplexRule}
        />
      </Modal>
      <div style={{ marginTop: 16 }}>
        <h3>模版代码</h3>
        <pre>
          {showRule ? (
            <RuleCodeGenerator rules={rules} newRuleContainer={showRule} />
          ) : (
            '点击查看详情按钮可查看模版代码'
          )}
        </pre>
      </div>
    </div>
  );
};

export default RulesConfig;
