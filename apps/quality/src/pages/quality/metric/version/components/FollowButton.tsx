import React, { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface FollowButtonProps {
  request: (input?: any) => Promise<boolean>;
  onFollow: (input?: any) => Promise<boolean>;
  onUnfollow: (input?: any) => Promise<boolean>;
}

enum FollowStatus {
  INIT,
  HAS_INIT,
  FOLLOWING,
  FOLLOWED,
  UNFLOWED,
}

const FollowButton: React.FC<FollowButtonProps> = ({ request, onFollow, onUnfollow }) => {
  const [status, setStatus] = useState<FollowStatus>(FollowStatus.INIT);
  const [following, setFollowing] = useState(false);

  useEffect(() => {
    request().then(isFollowing => {
      setFollowing(isFollowing);
      setStatus(FollowStatus.HAS_INIT);
    });
  }, []);

  function isDisable(s: FollowStatus) {
    return s === FollowStatus.INIT;
  }

  function isLoading(s: FollowStatus) {
    return s === FollowStatus.FOLLOWING;
  }

  const handleFollow = async () => {
    setStatus(FollowStatus.FOLLOWING);
    try {
      const success = await onFollow();
      if (!success) {
        throw new Error('关注失败');
      }
      setFollowing(true);
      message.success('关注成功');
      setStatus(FollowStatus.FOLLOWED);
    } catch (error) {
      message.error('关注失败');
      setStatus(FollowStatus.UNFLOWED);
    }
  };

  const handleUnfollow = async () => {
    setStatus(FollowStatus.FOLLOWING);
    try {
      const success = await onUnfollow();
      if (!success) {
        throw new Error('取消关注失败');
      }
      setStatus(FollowStatus.UNFLOWED);
      message.success('取消关注成功');
      setFollowing(false);
    } catch (error) {
      message.error('取消关注失败');
      setStatus(FollowStatus.FOLLOWED);
    }
  };

  return (
    <Button
      disabled={isDisable(status)}
      icon={following ? null : <PlusOutlined />}
      type={following ? 'default' : 'primary'}
      loading={isLoading(status)}
      onClick={following ? handleUnfollow : handleFollow}
    >
      {following ? '取消关注' : '关注'}
    </Button>
  );
};

export default FollowButton;
