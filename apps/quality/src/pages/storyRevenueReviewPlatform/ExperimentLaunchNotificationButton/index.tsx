import React, { useState } from 'react';
import { Button, Modal, Space, Toast, Avatar, Image, Tooltip } from '@douyinfe/semi-ui';
import { ChatInfo } from '@pa/shared/dist/src/core';
import ProFormSelector from '@/component/ProFormSelector';
import { searchLarkChat } from '@api/index';
import { sendExperimentLaunchNotification } from '@api/storyRevenueReviewPlatform';
import { Form } from 'antd';
import { buildLarkChatValue } from '@/pages/settings/slardar/autoLevel/SettingsItemFactory';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import iconPng from './icon.png';
/**
 * 实验上线通知按钮组件
 * 点击按钮弹出模态对话框，用于填写实验信息、需求信息，并选择要发送的聊天群组
 */
const ExperimentLaunchNotificationButton: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
}> = ({ taskInfo }) => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [isSending, setIsSending] = useState(false);

  // 显示弹窗
  const showModal = () => {
    setModalVisible(true);
  };

  const handleOk = async () => {
    try {
      // 先验证表单
      const values = await form.validateFields();
      setIsSending(true);
      if (!Array.isArray(values.chatGroups)) {
        values.chatGroups = [values.chatGroups];
      }
      // 处理群聊数据
      const chatGroups = values.chatGroups?.map((item: any) => {
        if (item.value) {
          try {
            return (JSON.parse(item.value) as ChatInfo).chat_id;
          } catch (error) {
            console.error('解析 JSON 失败:', error);
            return item as ChatInfo;
          }
        }
        return item as ChatInfo;
      });
      if (!taskInfo.revenueInfoCompleted || !taskInfo.fillInCompleted) {
        Toast.error('未填写完整');
        return;
      }
      // 调用后端API发送通知
      const result = await sendExperimentLaunchNotification({
        data: {
          story: taskInfo,
          chatGroups,
        },
      });

      if (result.code === 0) {
        Toast.success('发送成功');
        setModalVisible(false);
        form.resetFields();
      } else {
        Toast.error(`发送失败: ${result.message || '未知错误'}`);
      }
    } catch (error: any) {
      console.error('发送通知出错:', error);
      Toast.error(`发送失败: ${error.message || error.toString()}`);
    } finally {
      setIsSending(false);
    }
  };

  // 取消发送
  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  // 搜索群聊
  const searchLarkChatList = async (keyWard: string): Promise<ChatInfo[]> => {
    const result = await searchLarkChat({ data: { keyWords: keyWard } });
    return result || [];
  };
  const [toolTipVisible, setToolTipVisible] = useState(false);

  // // 获取当前日期字符串，格式为 YYYY-MM-DD
  // const getCurrentDate = () => {
  //   const date = new Date();
  //   const year = date.getFullYear();
  //   const month = String(date.getMonth() + 1).padStart(2, '0');
  //   const day = String(date.getDate()).padStart(2, '0');
  //   return `${year}-${month}-${day}`;
  // };

  return (
    <>
      <Tooltip content={'发送实验上线通知'} visible={toolTipVisible}>
        <Button
          icon={<img src={iconPng} alt="实验上线通知" style={{ width: 25, height: 25 }} />}
          theme={'borderless'}
          style={{
            color: 'rgba(var(--semi-grey-5), 1)',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '4px',
          }}
          onClick={showModal}
        />
      </Tooltip>
      <Modal
        title={'发送通知'}
        visible={modalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        closeOnEsc={true}
        confirmLoading={isSending}
        width={800}
      >
        <Form form={form} labelAlign="right">
          <ProFormSelector<ChatInfo>
            mode={'multiple'}
            form={form}
            name={'chatGroups'}
            label="选择群聊"
            initialValue={buildLarkChatValue({
              avatar:
                'https://s3-imfile.feishucdn.com/static-resource/v1/v2_99837451-df22-459c-9bea-78b0b6f99f6g~?image_size=100x100&cut_type=&quality=&format=jpeg&sticker_format=.webp',
              chat_id: 'oc_dbccc70bf083a8f4a85bdf017825c36d',
              name: '剪映CapCut产品改动上线/周知',
            })}
            placeholder="请输入群组名称搜索"
            buildSelectValue={buildLarkChatValue}
            search={searchLarkChatList}
            required
          />
        </Form>
      </Modal>
    </>
  );
};

export default ExperimentLaunchNotificationButton;
