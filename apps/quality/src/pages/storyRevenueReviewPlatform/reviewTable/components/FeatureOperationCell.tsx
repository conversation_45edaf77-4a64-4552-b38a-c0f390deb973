import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import { Button, Dropdown, Space, Toast } from '@douyinfe/semi-ui';
import React, { useState } from 'react';
import StoryRevenueTaskMeegoInfoUpdateButton from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueTaskMeegoInfoUpdateButton';
import ShowStoryRevenueInfoEditLogButton from '@/pages/storyRevenueReviewPlatform/reviewTable/components/ShowStoryRevenueInfoEditLogButton';
import { StoryRevenueReviewPeriodInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueReviewPeriodInfo';
import { IconMore, IconPlusCircle } from '@douyinfe/semi-icons';
import { batchSyncDataToMeego } from '@api/storyRevenueReviewPlatform';
import StoryRevenueTaskSyncDataToMeegoButton from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueTaskSyncDataToMeegoButton';
import StoryRevenueTaskBenefitsInfoUpdateByLibraApiButton from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueTaskBenefitsInfoUpdateButton';
import StoryRevenueTaskSyncFromOtherPeriodSheet from '@/pages/storyRevenueReviewPlatform/reviewTable/components/StoryRevenueTaskSyncFromOtherPeriodSheet';
import ExperimentLaunchNotificationButton from '@/pages/storyRevenueReviewPlatform/ExperimentLaunchNotificationButton';

const FeatureOperationCell: React.FC<{
  taskInfo: StoryRevenueTaskInfo;
  periodInfo?: StoryRevenueReviewPeriodInfo;
  onTaskInfoUpdate?: (newTaskInfo: StoryRevenueTaskInfo) => void;
}> = ({ taskInfo, periodInfo, onTaskInfoUpdate }) => {
  const [logPanelVisible, setLogPanelVisible] = useState(false);
  const [syncTaskFromOtherPeriodSheetVisible, setSyncTaskFromOtherPeriodSheetVisible] = useState(false);

  const handleTaskInfoUpdate = (newTaskInfo: StoryRevenueTaskInfo) => {
    if (onTaskInfoUpdate) {
      onTaskInfoUpdate(newTaskInfo);
    }
  };

  return (
    <Space>
      {/* <StoryRevenueTaskMeegoInfoUpdateButton taskInfo={taskInfo} onTaskInfoUpdate={handleTaskInfoUpdate} />*/}
      <ShowStoryRevenueInfoEditLogButton
        taskInfo={taskInfo}
        onVisibleChange={visible => {
          setLogPanelVisible(visible);
        }}
      />
      <ExperimentLaunchNotificationButton taskInfo={taskInfo} />
      <Dropdown
        trigger={'hover'}
        position={'bottomLeft'}
        clickToHide={true}
        render={
          <Dropdown.Menu>
            {periodInfo && !periodInfo.dsLocked && (
              <Dropdown.Item key={'key1'}>
                <StoryRevenueTaskMeegoInfoUpdateButton taskInfo={taskInfo} onTaskInfoUpdate={handleTaskInfoUpdate} />
              </Dropdown.Item>
            )}
            {periodInfo && !periodInfo.dsLocked && (
              <Dropdown.Item key={'key2'}>
                <StoryRevenueTaskBenefitsInfoUpdateByLibraApiButton
                  taskInfo={taskInfo}
                  periodInfo={periodInfo}
                  onTaskInfoUpdate={handleTaskInfoUpdate}
                />
              </Dropdown.Item>
            )}
            {periodInfo && periodInfo.dsLocked && (
              <Dropdown.Item key={'key3'}>
                <StoryRevenueTaskSyncDataToMeegoButton
                  taskInfo={taskInfo}
                  periodInfo={periodInfo}
                  onTaskInfoUpdate={handleTaskInfoUpdate}
                />
              </Dropdown.Item>
            )}
            {periodInfo && !periodInfo.dsLocked && periodInfo.enableSyncDataFromOtherPeriod && (
              <Dropdown.Item key={'key4'}>
                <Button
                  theme="borderless"
                  style={{ color: 'rgba(var(--semi-grey-5), 1)' }}
                  onClick={() => {
                    setSyncTaskFromOtherPeriodSheetVisible(true);
                  }}
                >
                  跨周期同步需求收益
                </Button>
              </Dropdown.Item>
            )}
          </Dropdown.Menu>
        }
      >
        <Button icon={<IconMore />} theme="borderless" style={{ color: 'rgba(var(--semi-grey-5), 1)' }} />
      </Dropdown>
      {syncTaskFromOtherPeriodSheetVisible && periodInfo && (
        <StoryRevenueTaskSyncFromOtherPeriodSheet
          visible={syncTaskFromOtherPeriodSheetVisible}
          taskInfo={taskInfo}
          periodInfo={periodInfo}
          onTaskInfoUpdate={handleTaskInfoUpdate}
          onVisibleChange={visible => {
            setSyncTaskFromOtherPeriodSheetVisible(visible);
          }}
        />
      )}
    </Space>
  );
};

export default FeatureOperationCell;
