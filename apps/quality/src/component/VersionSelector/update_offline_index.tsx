import React, { CSSProperties } from 'react';
import { message, TreeSelect } from 'antd';

// 定义树节点的数据结构，以便父组件使用
export interface TreeDataItem {
  value: string;
  title: React.ReactNode;
  children?: TreeDataItem[];
  disabled?: boolean;
}

export interface OfflineVersionSelectorProps {
  treeData: TreeDataItem[];
  value?: string;
  onChange?: (value: string | undefined) => void;
  isSingle?: boolean;
  style?: CSSProperties;
}

const OfflineVersionSelector: React.FC<OfflineVersionSelectorProps> = ({
  treeData,
  value,
  onChange,
  isSingle,
  style,
}) => (
  <TreeSelect
    showSearch={false}
    treeData={treeData}
    value={value}
    onChange={onChange}
    placeholder="请选择版本和阶段"
    treeDefaultExpandAll
    allowClear
    treeLine
    popupMatchSelectWidth={false}
    treeCheckable={!isSingle}
    style={style ?? { width: '100%' }}
  />
);

export default OfflineVersionSelector;
