import React, { useEffect, useState } from 'react';
import Data from '@/pages/quality/alarm/data';
import AlarmList from '@/pages/quality//alarm/list';
import { LoadInfo } from '@shared/loadInfo';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import UserSettingModule from '@/model/userSettingModel';
import { BrowserRouter, Route, Routes } from '@edenx/runtime/router';
import GraySlardar from '@/pages/versionProcess/graySlardar';
import { configure } from '@edenx/runtime/bff';
import { AuthInfo } from '@shared/kani/type';
import { Modal } from 'antd';
import resourceNameMap from '@shared/kani/resourceNameMap';
import { get_apply_url } from '@shared/kani/util';
import VersionSettingModel from '@/model/versionSettingModel';
import Compare from '@/pages/quality/version/compare';
import IssueLocation from '@/pages/tools/issueLocation';
import SlardarCreateBug from '@/pages/tools/slardarCreateBug';
import SlardarAssign from '@/pages/tools/slardarAssign';
import VersionModify from '@/pages/tools/versionModify';
import LibraInfo from '@/pages/versionProcess/libraInfo';
import QuotaDashboard from '@/pages/tools/quotaDashboard';
import ExperimentCrashMonitor from '@/pages/quality/experiment/experimentCrashMonitor';
import TeaMetricList from '@/pages/quality/metric/list';
import { Degradation } from '@/pages/quality/version/Degradation';
import { ControlRecordPage } from '@/pages/abtest/controlRecord';
import SlardarStatistic from '@/pages/quality/version/statistics';
import SlardarNewStatistic from '@/pages/quality/version/newStatistic';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import BuildInfoCard from '@/pages/settings/buildInfo';
import GrayTea from '@/pages/versionProcess/grayTea';
import TeaDashboard from '@/pages/quality/metric/dashboard';
import TeaTable from '@/pages/quality/metric/table';
import AutoLevelSettings from '@/pages/settings/slardar/autoLevel';
import SardarMetricsApproval from '@/pages/versionReleaseCenter/versionStageSlardarMetrics';
import VersionInfoModule from '@/model/versionInfoModel';
import VersionStageSettingModule from '@/model/versionStageSettingModel';
import MultiAttribution from '@/pages/quality/version/multiAttribution';
import QualityCheckListPanel from '@/pages/quality/metric/version/components/QualityCheckListPanel';
import { SceneOwnerPage } from './pages/quality/metric/sceneowner';
import MRProfilerTasks from './pages/quality/mrProfiler/tasks';
import MRProfilerCodeChange from './pages/quality/mrProfiler/codeChange';
import CheckItemModel from '@/model/CheckItemModel';
import StoryRevenueReviewPlatformHomePage from '@/pages/storyRevenueReviewPlatform';
import { QUALITY_HOST_CN_HTTPS, QUALITY_HOST_SG_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import MemoryGraphSettings from '@/pages/quality/memorygraph/settings';
import MemoryGraphIssueList from '@/pages/quality/memorygraph/detail';
import IOSQualityeUpdate from './pages/tools/iosqualityupdate';
import { getJwtToken, ReqRegion } from '@shared/utils/basic_auth';
import LibraCreatePage from '@/pages/libra/create';
import LibraDesignPage from '@/pages/libra/design';
import LibraGovernancePage from '@/pages/libra/governance';
import LibraListPage from '@/pages/libra/list';
import LibraNotificationPage from '@/pages/libra/notification';
import LibraPatrolPage from '@/pages/libra/patrol';
import LibraReviewPage from '@/pages/libra/review';
import LibraStatisticsPage from '@/pages/libra/statistics';
import LibraDebugPage from '@/pages/libra/debug';
import VersionLibraListPage from '@/pages/libra/list/VersionLibraListPage';
import { LibraAttribution } from '@/pages/quality/libra/LibraAttribution';
import AlogReaderPage from '@/pages/quality/alog/reader';
import LibraPermissionManage from '@/pages/settings/libra/LibraPermissionManage';
import MeegoTeamManagePage from '@/pages/settings/libra/MeegoTeamManagePage';
import LibraStatisticsDashboardByMetricPage from '@/pages/libra/statistics/dashboard_by_metric';
import LibraStatisticsDashboardByFlightPage from '@/pages/libra/statistics/dashboard_by_flight';
import LibraStatisticsWeeklyReportsPage from '@/pages/libra/statistics/weekly_reports';
import LibraEventPage from '@/pages/libra/event';
import LibraSignificanceStatisticsPage from '@/pages/libra/statistics/significance_statistics';
import NewStageMetricApproval from '@/pages/versionReleaseCenter/versionStageMetricsApproval/NewStageMetricApproval';
import StoryBenefitsDashboardPage from '@/pages/storyRevenueReviewPlatform/Dashboard';
import VersionCodeGenerate from '@/component/VersionCodeGenerate';
import { MetricItemInfo } from '@shared/releasePlatform/versionStageInfoCheckList';
import LibraExemptPage from '@/pages/libra/exempt/exemptList';
import LVMobileLibraReopenStatisticsPage from '@/pages/libra/statistics/lv_mobile_libra_reopen';
import LibraLarkSidebarPage from '@/pages/libra/lark-sidebar-submit/LibraLarkSidebarPage';
import LibraAutoCreatePage from '@/pages/libra/auto-create/LibraAutoCreatePage';
import LibraManageDebugPage from '@/pages/settings/libra/LibraManageDebugPage';
import LVMobileLibraReopenRecentStatisticsPage from '@/pages/libra/statistics/lv_mobile_libra_reopen_recent';
import LVPCLibraReopenRecentStatisticsPage from '@/pages/libra/statistics/lv_pc_libra_reopen_recent';
import RetouchHypicLibraReopenRecentStatisticsPage from '@/pages/libra/statistics/retouch_hypic_libra_reopen_recent';
import LibraStatisticsDashboardOverviewPage from '@/pages/libra/statistics/dashboard_overview';
import OfflineSlardar from '@/pages/versionProcess/offlineSlardar';

const Setting = (prop: any) => {
  const [appSettingStatus, appSettingActions] = useModel(AppSettingModule);
  const [userSettings, userSettingActions] = useModel(UserSettingModule);
  const [versionSettingState, versionSettingAction] = useModel(VersionSettingModel);
  const [versionProcessInfo, versionProcessAction] = useModel(VersionInfoModule);
  const [stageSettingState, stageSettingAction] = useModel(VersionStageSettingModule);
  const [jwt, setJwt] = useState<string | undefined>(undefined);
  const [checkItemState, updateCheckItemAction] = useModel(CheckItemModel);

  dayjs.extend(customParseFormat);
  dayjs.extend(advancedFormat);
  dayjs.extend(weekday);
  dayjs.extend(localeData);
  dayjs.extend(weekOfYear);
  dayjs.extend(weekYear);

  if (!window.__GARFISH__) {
    return (
      <BrowserRouter basename={'/'}>
        <Routes>
          <Route index element={<>您直接访问了微前端页面！请访问纸飞机主页: https://pa.bytedance.net/</>} />
        </Routes>
      </BrowserRouter>
    );
  }
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    const handleStatus = (message: LoadInfo) => {
      console.log('[MicroFronted]receive status:', message);
      if (message.appInfo) {
        appSettingActions.updateSelectAppInfo(message.appInfo);
      }
      if (message.loginInfo) {
        userSettingActions.setUserSetting(message.loginInfo);
        setJwt(message.loginInfo.jwt);
      }
      if (message.versionInfo?.lvVersion || message.versionInfo?.normalVersion) {
        versionSettingAction.updateSelectVersionInfo(message.versionInfo);
      }
      if (message.versionProcessModel) {
        versionProcessAction.updateCurrentVersionInfo(message.versionProcessModel);
      }
      if (message.stageInfo) {
        stageSettingAction.updateCurrentStage(message.stageInfo);
      }
      if (message.checkItem) {
        updateCheckItemAction.updateCheckItemState(message.checkItem);
        console.log('checkItem:', message.checkItem);
      }
    };
    window?.Garfish.channel.on('status', handleStatus); // 注册监听事件
    window?.Garfish.channel.emit('loaded', 'loaded'); // 发送事件注册完毕，父应用发送配置信息
  }, []);

  if (!jwt) {
    return <></>;
  }

  configure({
    interceptor(request) {
      return async (url, params) => {
        const isLocalhost = /^(localhost|127(?:\.[0-9]+){0,2}\.[0-9]+|\[::1])(:\d+)?$/.test(window.location.host);
        const host = () => {
          if (!isLocalhost && (url as string).startsWith('/api/quality/cn')) {
            return QUALITY_HOST_CN_HTTPS;
          }
          if (!isLocalhost && (url as string).startsWith('/api/quality/sg')) {
            return QUALITY_HOST_SG_HTTPS;
          }
          return '';
        };
        try {
          if (!userSettings.info.jwt || userSettings.info.jwt.length <= 0) {
            return {};
          }

          const headers = params?.headers as any;
          headers['x-jwt-token'] = userSettings.info.jwt;
          headers['x-jwt-token-v2'] = await getJwtToken(
            appSettingStatus.info.businessInfo.oversea ? ReqRegion.I18n : ReqRegion.CN,
          );
          headers['app-setting'] = encodeURIComponent(JSON.stringify(appSettingStatus.info));
          headers['version-setting'] = encodeURIComponent(JSON.stringify(versionSettingState.info));
          // console.log('headers', headers);
          // headers['user-email'] = userSettings.info.email;
          // headers['pa-test-user'] = userSettings.info.email;
          const res = await request(`${host()}${url}`, params);
          const result = await res.json();
          if (result.auth_info) {
            const info = result.auth_info as AuthInfo;
            Modal.error({
              title: '暂无权限',
              content: (
                <>
                  您暂无对 {resourceNameMap[info.resource_key]} 资源的 {info.permission_name} 权限。您可以前往
                  <a
                    href={get_apply_url(info.resource_key, info.permission_name)}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    此处
                  </a>
                  申请该权限。敬请谅解。
                </>
              ),
            });
          }
          return result;
        } catch (e) {
          console.log(`interceptErr`, e);
        }
      };
    },
  });

  const getReleasePage = (_checkItemState: any) => {
    if (_checkItemState.info?.check_item_id === 'dummy_version_libra_list') {
      return <VersionLibraListPage />;
    } else if (_checkItemState.info?.check_item_id?.includes('teaMetric')) {
      return (
        <>
          <NewStageMetricApproval
            name={(_checkItemState.info?.item_info as MetricItemInfo).name}
            version={versionProcessInfo?.info?.version}
            appId={versionProcessInfo?.info?.app_id}
          />
        </>
      );
    } else {
      return <SardarMetricsApproval />;
    }
  };

  return (
    <BrowserRouter basename={'/'}>
      <Routes>
        <Route index element={<>HHHHH</>} />
        <Route path={'quality/alarm/data'} element={<Data />} />
        <Route path={'quality/alarm/list'} element={<AlarmList />} />
        <Route path={'settings/quality/metric-config'} element={<TeaMetricList />} />
        <Route path={'settings/libra/permission-manage'} element={<LibraPermissionManage />} />
        <Route path={'settings/libra/meego-team-manage'} element={<MeegoTeamManagePage />} />
        <Route path={'settings/libra/debug-page'} element={<LibraManageDebugPage />} />
        <Route path={'quality/statistics/performance/version'} element={<QualityCheckListPanel />} />
        <Route path={'quality/statistics/performance/dashboard'} element={<TeaDashboard />} />
        <Route path={'quality/statistics/performance/table'} element={<TeaTable />} />
        <Route path={'settings/quality/scene-owner'} element={<SceneOwnerPage />} />
        <Route path={'quality/diagnosis/issue-attribution/compare'} element={<Compare />} />
        <Route path={'quality/diagnosis/issue-attribution/escape'} element={<Degradation />} />
        <Route path={'quality/diagnosis/issue-attribution/statistics'} element={<SlardarStatistic />} />
        <Route path={'quality/diagnosis/issue-attribution/statistics/new'} element={<SlardarNewStatistic />} />
        <Route path={'quality/inspect/crash'} element={<ExperimentCrashMonitor />} />
        <Route path={'quality/diagnosis/issue-attribution/offline-stability-metric/*'} element={<OfflineSlardar />} />
        <Route path={'quality/diagnosis/issue-attribution/gray-stability-metric/*'} element={<GraySlardar />} />
        <Route path={'quality/diagnosis/issue-attribution/mr-profile/task-list'} element={<MRProfilerTasks />} />
        <Route path={'quality/diagnosis/issue-attribution/mr-profile/code-change'} element={<MRProfilerCodeChange />} />
        <Route path={'quality/statistics/performance/tea'} element={<GrayTea />} />
        <Route path={'quality/quick-access/compare'} element={<Compare />} />
        <Route path={'quality/quick-access/create-bug'} element={<SlardarCreateBug />} />
        <Route path={'quality/quick-access/gray-stability-metric/*'} element={<GraySlardar />} />
        <Route path={'quality/quick-access/offline-stability-metric/*'} element={<OfflineSlardar />} />
        <Route path={'releaseProcess/slardar/*'} element={<GraySlardar />} />
        <Route path={'releaseProcess/tea'} element={<GrayTea />} />
        <Route path={'quality/diagnosis/tools/slardar/troubleshooting'} element={<IssueLocation />} />
        <Route path={'quality/diagnosis/tools/slardar/create-bug'} element={<SlardarCreateBug />} />
        <Route path={'quality/diagnosis/tools/slardar/auto-assign'} element={<SlardarAssign />} />
        <Route path={'quality/diagnosis/tools/slardar/version-update'} element={<VersionModify />} />
        <Route path={'quality/diagnosis/tools/slardar/quota-dashboard'} element={<QuotaDashboard />} />
        <Route path={'quality/diagnosis/tools/slardar/data-update'} element={<IOSQualityeUpdate />} />
        <Route path={'settings/developer/build-info/quality'} element={<BuildInfoCard />} />
        <Route path={'settings/quality/slardar/auto-level'} element={<AutoLevelSettings />} />
        <Route path={'release/list'} element={getReleasePage(checkItemState)} />
        <Route path={'quality/diagnosis/issue-attribution/multi-attribution'} element={<MultiAttribution />} />
        <Route path={'benefits/list'} element={<StoryRevenueReviewPlatformHomePage />} />
        <Route path={'benefits/dashboard'} element={<StoryBenefitsDashboardPage />} />
        <Route path={'quality/diagnosis/issue-attribution/memory-graph/settings'} element={<MemoryGraphSettings />} />
        <Route path={'quality/diagnosis/issue-attribution/memory-graph/detail'} element={<MemoryGraphIssueList />} />
        <Route path={'quality/diagnosis/issue-attribution/libra-attribution'} element={<LibraAttribution />} />
        <Route path={'quality/diagnosis/issue-attribution/alog/reader'} element={<AlogReaderPage />} />
        {/* <Route path={'quality/diagnosis/issue-attribution/alog/settings'} element={<AlogSettingsPage />} />*/}
        <Route path={'libra/control/remind'} element={<LibraInfo />} />
        <Route path={'libra/control/event'} element={<LibraEventPage />} />
        <Route path={'libra/control/exempt'} element={<LibraExemptPage />} />
        <Route path={'libra/control/record'} element={<ControlRecordPage />} />
        <Route path={'libra/create'} element={<LibraCreatePage />} />
        <Route path={'libra/design'} element={<LibraDesignPage />} />
        <Route path={'libra/governance'} element={<LibraGovernancePage />} />
        <Route path={'libra/list'} element={<LibraListPage />} />
        <Route path={'libra/notification'} element={<LibraNotificationPage />} />
        <Route path={'libra/patrol'} element={<LibraPatrolPage />} />
        <Route path={'libra/patrol/stability'} element={<ExperimentCrashMonitor />} />
        <Route path={'libra/review'} element={<LibraReviewPage />} />
        <Route path={'libra/statistics'} element={<LibraStatisticsPage />} />
        <Route path={'libra/statistics/reopen/lv_mobile'} element={<LVMobileLibraReopenStatisticsPage />} />
        <Route
          path={'libra/statistics/reopen/lv_mobile_recent'}
          element={<LVMobileLibraReopenRecentStatisticsPage />}
        />
        <Route path={'libra/statistics/reopen/lv_pc_recent'} element={<LVPCLibraReopenRecentStatisticsPage />} />
        <Route
          path={'libra/statistics/reopen/retouch_hypic_recent'}
          element={<RetouchHypicLibraReopenRecentStatisticsPage />}
        />
        <Route path={'libra/statistics/dashboard_by_metric'} element={<LibraStatisticsDashboardByMetricPage />} />
        <Route path={'libra/statistics/dashboard_by_flight'} element={<LibraStatisticsDashboardByFlightPage />} />
        <Route path={'libra/statistics/dashboard_overview'} element={<LibraStatisticsDashboardOverviewPage />} />
        <Route path={'libra/statistics/significance_statistics'} element={<LibraSignificanceStatisticsPage />} />
        <Route path={'libra/statistics/weekly_reports'} element={<LibraStatisticsWeeklyReportsPage />} />
        <Route path={'libra/lark-sidebar-submit'} element={<LibraLarkSidebarPage />} />
        <Route path={'libra/auto-create'} element={<LibraAutoCreatePage />} />
        <Route path={'libra/debug'} element={<LibraDebugPage />} />
        <Route path={'libra/tools/version-code'} element={<VersionCodeGenerate />} />
      </Routes>
    </BrowserRouter>
  );
};
export default Setting;
