import { BusMeta } from '@shared/bus/busMeta';
import { MrIn<PERSON>, MrReviewerInfo } from '@shared/bits/mrInfo';
import { Inject, Injectable } from '@gulux/gulux';
import LarkService from '@pa/backend/dist/src/third/lark';
import BitsService from '../third/bits';
import LarkCardService from '../larkCard';
import {
  CardActionElement,
  CardActionValue,
  CardButtonAction,
  CardButtonType,
  CardCallbackType,
  CardElement,
  CardElementTag,
  CardTemplate,
  CardTextTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { BatchSubMrInfoTable } from '../../model/BatchSubMrInfoTable';
import { BatchSubMrRelatedMrStatus, BatchRelationMiddleMr } from '@shared/bus/BatchMrModel';
import repos from '@shared/gitlab/repos';
import { BusBatchInfoTable } from '../../model/BusBatchInfoTable';
import { VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { GitlabMrResponse } from '@shared/gitlab';

@Injectable()
export default class BatchMrCardService {
  @Inject()
  private lark: LarkService;

  @Inject()
  private bits: BitsService;

  @Inject()
  private larkCard: LarkCardService;

  /**
   * Batch Mr 预告
   */
  buildBatchMrNoticeCard(version: string, mrInfos: MrReviewerInfo[], versionInfo?: VersionProcessInfo) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] Batch Mr预告`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `为加快封版进度，以下MMR将进入Batch Mr流程，请注意！对于有冲突的MMR，请根据提示在影子分支解决冲突，提交后自动合入Batch分支中`,
      },
    } as CardElement);
    mrInfos.forEach(mrInfo => {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[${mrInfo.mrInfo.title}](${mrInfo.mrInfo.mr_detail_url}), <at email="${mrInfo.mrInfo.author}@bytedance.com"></at>`,
        },
      } as CardElement);
    });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**<font color='red'>！！！在影子分支解决冲突不会影响原MR，收到解冲突提醒时请最高优先级解决影子分支的冲突，解完即可封版，否则源MR无法合入！！！</font>**`,
        text_size: 'heading',
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `[Batch Mr执行机制文档](https://bytedance.larkoffice.com/docx/YxZsdTbaIodKbKxE0Swc1dMdnfN)`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 取消Batch成功通知
   */
  buildCancelBatchCard(mrInfo: MrInfo, succed: boolean, version: string, reason: string) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] 取消Batch`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    if (succed) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `<at email="${mrInfo.author}@bytedance.com">, 你的Mr [${mrInfo.title}](${mrInfo.mr_detail_url}) 由于 ${reason} 已取消Batch`,
        },
      } as CardElement);
    } else {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `取消失败`,
        },
      } as CardElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 催促解决冲突
   */
  buildBatchSubmrConflictCard(
    mrInfo: MrInfo,
    batchSubMrInfo: BatchSubMrInfoTable,
    batchInfo: BusBatchInfoTable,
    version: string,
  ) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] Batch子MR冲突`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email="${mrInfo.author}@bytedance.com"></at>, 你的Mr [${mrInfo.title}](${mrInfo.mr_detail_url}) 存在冲突，请尽快解决，以下为冲突的仓库：`,
      },
    } as CardElement);
    const conflictMrs = batchSubMrInfo.relationMiddleMrs.filter(
      mr => mr.status === BatchSubMrRelatedMrStatus.Conflicting,
    );
    for (let i = 0; i < conflictMrs.length; i++) {
      const conflictMr = conflictMrs[i];
      const projInfo = repos.searchProjectInfo({ projectId: conflictMr.projectId });
      if (!projInfo) {
        continue;
      }
      const conflictUrl = `${conflictMr.middleMrUrl}/conflicts`;
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `冲突仓库${i + 1}：${projInfo.projectName}，**[点此链接在线解冲突](${conflictUrl})**`,
        },
      } as CardElement);
    }
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**<font color='red'>!!! 请「在线解冲突」，或「严格」按照以下步骤本地解冲突，push后无需额外处理，会自动合入到Batch分支中 !!!</font>**`,
        text_size: 'heading',
      },
    } as CardElement);
    const subBranchName = batchSubMrInfo.relationMiddleMrs[0].middleBranch;
    const resolveConflictSteps = [
      'git fetch origin',
      `git checkout -b ${subBranchName} origin/${subBranchName}`,
      `git merge origin/${batchInfo.batchBranchName}`,
      `解决冲突并commit`,
      `git push`,
    ];
    elements.push({
      tag: CardElementTag.markdown,
      content: `\`\`\`JSON
${resolveConflictSteps.join('\n')}
\`\`\``,
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**<font color='red'>!!! 冲突分支为影子分支，不会影响原MR，解完合入Batch分支即可封版 !!!</font>**`,
        text_size: 'heading',
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '已处理完',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.BatchSubMrConflictResolved,
            mrId: mrInfo.id.toString(),
            busId: 'off_line_bus',
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 中间分支同步冲突提醒
   */
  buildBatchSubmrSyncConflictCard(
    mrInfo: MrInfo,
    batchSubMrInfo: BatchSubMrInfoTable,
    batchInfo: BusBatchInfoTable,
    version: string,
    conflictMrs: GitlabMrResponse[],
  ) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] Batch子MR同步冲突`,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email="${mrInfo.author}@bytedance.com"></at>, 你的Mr [${mrInfo.title}](${mrInfo.mr_detail_url}) 在同步原分支最新代码时存在冲突，请尽快解决，以下为冲突的仓库：`,
      },
    } as CardElement);

    for (let i = 0; i < conflictMrs.length; i++) {
      const conflictMr = conflictMrs[i];
      const projInfo = repos.searchProjectInfo({ projectId: conflictMr.project_id });
      if (!projInfo) {
        continue;
      }
      const conflictUrl = `${conflictMr.web_url}/conflicts`;
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `冲突仓库${i + 1}：${projInfo.projectName}，**[点此链接在线解冲突](${conflictUrl})**`,
        },
      } as CardElement);
    }

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**<font color='red'>!!! 这是在同步原分支最新代码时产生的冲突，解决后将自动重新尝试合入Batch分支 !!!</font>**`,
        text_size: 'heading',
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '已处理完',
          },
          type: CardButtonType.primary,
          value: {
            cardCallbackType: CardCallbackType.BatchSubMrConflictResolved,
            mrId: mrInfo.id.toString(),
            busId: 'off_line_bus',
          } as CardActionValue,
        } as CardButtonAction,
      ],
    } as CardActionElement);

    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * 合入Batch提醒
   */
  buildOnBatchCard(mrInfo: MrInfo, batchSubMrInfo: BatchSubMrInfoTable, version: string) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] 子Mr合入Batch分支提醒`,
      template: CardTemplate.orange,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email="${mrInfo.author}@bytedance.com"></at>, 你的Mr [${mrInfo.title}](${mrInfo.mr_detail_url}) 已合入Batch分支`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `请勿关闭原本的MR，在Batch Mr合入develop，原本Mr合入不受影响`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `**请不要在原MR解冲突！尽快让原MR的Pipeline完成！**`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * Batch Mr创建成功提醒
   */
  buildBatchMrLaunchCard(mrInfo: MrInfo, version: string, subMrList: MrInfo[]) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${version}] Batch Mr启动`,
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `子Mr已全部合入到Batch分支，Batch Mr已创建成功，并锁定为super mr，原Mr的冲突移步到Batch Mr中解决，子Mr列表：`,
      },
    } as CardElement);
    subMrList.forEach(subMr => {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[${subMr.title}](${subMr.mr_detail_url}), <at email="${subMr.author}@bytedance.com"></at>`,
        },
      } as CardElement);
    });
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `Batch Mr合入条件：1.各端出包成功；2.各子Mr Pipeline通过；3.各子Mr review通过`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `子Mr Owner请不要再更新相应Mr状态/提交代码/解决冲突，尽快让Mr达到合入条件，新增冲突以Batch Mr为准`,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `Batch Mr链接：[${mrInfo.title}](${mrInfo.mr_detail_url})`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  buildBatchMrInfoCard(mrInfo: MrInfo[]) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `Batch Mr-子Mr信息`,
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    mrInfo.forEach(subMr => {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `[${subMr.title}](${subMr.mr_detail_url})`,
        },
      } as CardElement);
    });
    baseCard.elements = elements;
    return baseCard;
  }
  /**
   * Batch Mr合入/废弃提醒
   */
  buildBatchMrCompletedCard(mrInfo: MrInfo, bus: BusMeta, merged: boolean) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `[${bus.version}] Batch Mr完成`,
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: merged ? `Batch Mr已合入，本轮Batch结束` : `Batch Mr已关闭，本轮Batch废弃`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }
  /**
   * Batch subMr合入问题告知
   */
  buildSubMrMergeIssueCard(mrInfo: MrInfo, url: string) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `子Mr合并失败`,
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `<at email="<EMAIL>"></at>, 子Mr: [${mrInfo.title}](${mrInfo.mr_detail_url})合并Batch失败，请关注: ${url}`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  /**
   * subMr回滚失败卡片
   */
  buildSubMrRevertFailedCard(urls: string[]) {
    const baseCard = this.larkCard.buildBaseCard({
      title: `子Mr回滚失败，请BM手动处理，关联gitlab mr：`,
      template: CardTemplate.purple,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    urls.forEach(url => {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `${url}`,
        },
      } as CardElement);
    });
    baseCard.elements = elements;
    return baseCard;
  }
}
