import { Inject, Injectable } from '@gulux/gulux';
import { BusMrTable } from '../../model/BusMr';
import { ModelType } from '@gulux/gulux/typegoose';
import BitsService from '../third/bits';
import BusCardService from './busCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import BusDao from '../dao/BusDao';
import { TccClients } from '@gulux/gulux/tcc';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { isMultiMr, MrInfo, MrReviewerInfo, MrState, MrType } from '@shared/bits/mrInfo';
import GitLabService from '../third/gitlab';
import { nanoid } from 'nanoid';
import { NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import { BusMr, BusMrStatus } from '@shared/bus/busMr';
import { compact, fromPairs, head, pick, values } from 'lodash';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { BusBatchInfoTable } from '../../model/BusBatchInfoTable';
import {
  BatchRelationMiddleMr,
  BatchSubMrInfo,
  BatchSubMrRelatedMrStatus,
  BatchSubMrStatus,
  BusBatchInfo,
  BusBatchStatus,
} from '@shared/bus/BatchMrModel';
import { BatchSubMrInfoTable } from '../../model/BatchSubMrInfoTable';
import repos from '@shared/gitlab/repos';
import BitsConfigV2Service, { MrRepos } from '../bitsConfigV2';
import BatchMrCardService from './BatchMrCard';
import { CardCallback } from '@pa/shared/dist/src/lark/larkCard';
import { trim_suffix } from '@pa/shared/dist/src/utils/tools';
import { BuildMasterInfo, LVProductType } from '@shared/process/versionProcess';
import { BmType } from '@shared/bits/bmInfo';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import VersionProcessDao from '../dao/VersionProcessDao';
import dayjs from 'dayjs';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { NetworkX } from '../../utils/NetworkX';
import { BatchMRReportInfo, BatchMRReportType } from '@shared/releasePlatform/batchMRReportInfo';
import BatchMRReportInfoDao from '../dao/releasePlatform/BatchMRReportInfoDao';
import { GitlabMrResponse } from '@shared/gitlab';

interface BatchMrSwitch {
  enable: boolean;
}
@Injectable()
export default class BatchMrService {
  @Inject(BusMrTable)
  private busMrModel: ModelType<BusMrTable>;

  @Inject(BusBatchInfoTable)
  private busBatchModel: ModelType<BusBatchInfoTable>;

  @Inject(BatchSubMrInfoTable)
  private batchSubMrInfoModel: ModelType<BatchSubMrInfoTable>;

  @Inject()
  private bits: BitsService;

  @Inject()
  private busCard: BusCardService;

  @Inject()
  private batchMrCard: BatchMrCardService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private busDao: BusDao;

  @Inject()
  private readonly tccClients!: TccClients;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private gitlab: GitLabService;

  @Inject()
  private versionProcessDao: VersionProcessDao;

  @Inject()
  private bitsConfigV2Service: BitsConfigV2Service;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private batchMRReportInfoDao: BatchMRReportInfoDao;

  async autoPushOnProgressVersionBatchMr() {
    const currentBus = await this.busDao.queryCurrentIntegrationBus();
    if (!currentBus) {
      return false;
    }
    await this.syncSubMrs(currentBus.version);
    await this.checkWaitingMr(true, currentBus.version);
  }

  async fetchAllBatchAndSubMr(version: string): Promise<{ batchInfo: BusBatchInfo; subMrInfo: BatchSubMrInfo[] }[]> {
    const batchInfos: BusBatchInfo[] | null = await this.busBatchModel.find({ version });
    if (!batchInfos) {
      return [];
    }
    const res: { batchInfo: BusBatchInfo; subMrInfo: BatchSubMrInfo[] }[] = [];
    for (const batchInfo of batchInfos) {
      const subMrs: BatchSubMrInfo[] = await this.batchSubMrInfoModel.find({ version, batchKey: batchInfo.batchKey });
      res.push({ batchInfo, subMrInfo: subMrs });
    }
    return res;
  }

  async terminateBatchMr(batchKey: string, complete?: boolean) {
    const batchInfo: BusBatchInfo | null = await this.busBatchModel.findOne({ batchKey });
    if (!batchInfo) {
      return;
    }
    batchInfo.batchStatus = complete ? BusBatchStatus.Merged : BusBatchStatus.Abandoned;
    await this.busBatchModel.updateOne({ batchKey }, batchInfo);
  }

  async markSubMrOnBatch(mrId: number, batchKey: string) {
    const subMrInfo: BatchSubMrInfo | null = await this.batchSubMrInfoModel.findOne({ batchKey, originMrId: mrId });
    const mrInfo = await this.bits.getMrInfo({ mrId });
    if (subMrInfo) {
      if (mrInfo?.state === MrState.merged || mrInfo?.state === MrState.forceMerged) {
        subMrInfo.status = BatchSubMrStatus.Merged;
      } else {
        subMrInfo.status = BatchSubMrStatus.OnBatch;
      }
      await this.batchSubMrInfoModel.updateOne({ batchKey, originMrId: mrId }, subMrInfo);
    }
  }

  async startBatchProcess() {
    const currentBus = await this.busDao.queryCurrentIntegrationBus();
    if (!currentBus) {
      return false;
    }
    const switchOn = await this.isBatchMrSwitchOn();
    if (!switchOn) {
      this.logger.info(`[Batch Mr] return because switch off`);
      return false;
    }
    if (dayjs().utcOffset(8).hour() < 16) {
      this.logger.info(`[Batch Mr] return because not in 16 - 24`);
      return;
    }
    const currentBatchMrInfo = await this.busBatchModel.findOne({
      version: currentBus.version,
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (currentBatchMrInfo) {
      this.logger.info(`[Batch Mr] return because batch mr is on processing`);
      return false;
    }

    const allUnmergedMr: BusMr[] = await this.busMrModel.find({
      busId: currentBus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });

    const unMergedMmrs = await this.bits
      .getMrInfoAndReviewer(allUnmergedMr.map(mr => mr.mrId))
      .then(mrInfos => mrInfos.filter(mrInfo => isMultiMr(mrInfo.mrInfo)));

    this.logger.info(`[Batch Mr] unmerged mmrs : ${unMergedMmrs.map(mmr => mmr.mrInfo.title)}`);
    if (unMergedMmrs.length <= 1) {
      this.logger.info(`[Batch Mr] return because un merged mmr count < 1`);
      return false;
    }

    // 创建版本Batch信息
    const busBatchInfo: BusBatchInfoTable = {} as BusBatchInfoTable;
    busBatchInfo.busId = currentBus._id ?? '';
    busBatchInfo.version = currentBus.version;
    const versionBatchInfos: BusBatchInfoTable[] = await this.busBatchModel.find({
      version: currentBus.version,
    });
    busBatchInfo.batchKey = `Batch_${currentBus.version}_count_${versionBatchInfos.length}`;
    busBatchInfo.batchStatus = BusBatchStatus.OnGoing;
    busBatchInfo.protocolIds = [];

    // 拉群
    const newBatchInfo = await this.createBatchGroup(currentBus.version, unMergedMmrs, busBatchInfo);
    if (!newBatchInfo) {
      return false;
    }
    await this.createBatchMr(unMergedMmrs, newBatchInfo, currentBus.version);
    return true;
  }

  async startBatchWithMrs(mrIds: number[], batchVersion?: string) {
    const unMergedMmrs = await this.bits.getMrInfoAndReviewer(mrIds);

    this.logger.info(`[Batch Mr] unmerged mmrs : ${unMergedMmrs.map(mmr => mmr.mrInfo.title)}`);
    if (unMergedMmrs.length <= 1) {
      this.logger.info(`[Batch Mr] return because un merged mmr count < 1`);
      // return false;
    }

    const version = batchVersion ?? (await this.busVersion());

    // 创建版本Batch信息
    const busBatchInfo: BusBatchInfoTable = {} as BusBatchInfoTable;
    busBatchInfo.busId = 'off_line_bus';
    busBatchInfo.version = version;
    const versionBatchInfos: BusBatchInfoTable[] = await this.busBatchModel.find({
      version,
    });
    busBatchInfo.batchKey = `Batch_${version.split('.').join('_')}_count_${versionBatchInfos.length}`;
    busBatchInfo.batchStatus = BusBatchStatus.OnGoing;

    // 拉群
    const newBatchInfo = await this.createBatchGroup(version, unMergedMmrs, busBatchInfo);
    if (!newBatchInfo) {
      return false;
    }
    await this.createBatchMr(unMergedMmrs, newBatchInfo, version);
    return true;
  }

  async addSubMr(mrId: number, version: string) {
    const mrReviewInfo = head(await this.bits.getMrInfoAndReviewer([mrId]));
    if (!mrReviewInfo) {
      return;
    }
    const busBatchInfo = await this.busBatchModel
      .findOne({
        batchStatus: BusBatchStatus.OnGoing,
        version,
      })
      .exec();
    if (!busBatchInfo) {
      return;
    }
    const relatinoMrs = await this.bits.getMrRelationList(mrId);
    if (!relatinoMrs) {
      return;
    }
    const relationInfos = await this.bits.fetchAllMrInfo(relatinoMrs.map(it => it.id));
    relationInfos.push(mrReviewInfo.mrInfo);
    for (const relationInfo of relationInfos) {
      if (relationInfo) {
        await this.tryAddProject(relationInfo, busBatchInfo);
      }
    }
    const ids = await this.getMrAuthorIds([mrReviewInfo.mrInfo.author]);
    try {
      await this.lark.addUserToChatGroup(busBatchInfo.chatId, UserIdType.openId, Object.values(ids));
    } catch (e) {
      throw e;
    }
    await this.mergeSubMr(mrReviewInfo.mrInfo, busBatchInfo.batchBranchName, busBatchInfo, version);
  }

  async tryAddProject(mrInfo: MrInfo, batchInfo: BusBatchInfoTable) {
    const projInfo = repos.searchProjectInfo({ projectId: mrInfo.project_id });
    if (!projInfo) {
      return;
    }
    if (batchInfo.mrRepos.find(it => it.projectId === mrInfo.project_id)) {
      return;
    }
    const createBranchRet = await this.gitlab.createBranch(
      mrInfo.project_id,
      batchInfo.batchBranchName,
      mrInfo.target_branch,
    );
    if (createBranchRet.code === NetworkCode.Error) {
      throw new Error(createBranchRet.message);
    }
    const mainRepos = repos.main_repos;
    batchInfo.mrRepos.push({
      projectId: mrInfo.project_id,
      sourcesBranch: batchInfo.batchBranchName,
      targetBranch: mrInfo.target_branch,
      isHost: mainRepos.find(it => it.projectId === mrInfo.project_id) !== undefined,
      platform: projInfo.platform,
    });
    await this.gitlab.protectBranch(mrInfo.project_id, batchInfo.batchBranchName);
    await this.busBatchModel.updateOne(
      {
        batchKey: batchInfo.batchKey,
      },
      batchInfo,
    );
  }

  /**
   * helper function，限制进入batch mr的仅限7仓的mr
   * @param mrInfos 传入的所有mr
   * @return validMrInfos 所有关联mr全部为7仓主干分支的mr
   */
  private async filterAndValidateMrs(mrInfos: MrReviewerInfo[]): Promise<MrReviewerInfo[]> {
    this.logger.info(`[Batch Mr] Start filtering and validating MRs against main repos`);
    const mainRepos = new Map([
      [39995, 'rc/develop'], // iosMainRepo
      [798331, 'rc/develop'], // iosTTPMainRepo
      [40279, 'rc/develop'], // androidMainRepo
      [798270, 'rc/develop'], // androidTTPMainRepo
      [142582, 'rc/develop'], // pcMainRepo
      [802470, 'rc/develop'], // pcTTPMainRepo
      [134421, 'rc/develop'], // middleLayerRepo
    ]);

    const validMrInfos: MrReviewerInfo[] = [];
    for (const mrInfo of mrInfos) {
      const allRelationMrs = await this.bits.getMrRelationList(mrInfo.mr_id);
      let mrGroup: MrReviewerInfo[] = [mrInfo];
      if (allRelationMrs) {
        const relationMrInfos = await this.bits.getMrInfoAndReviewer(allRelationMrs.map(r => r.id));
        mrGroup = mrGroup.concat(relationMrInfos);
      }
      const isGroupValid = mrGroup.every(mr => {
        const requiredBranch = mainRepos.get(mr.mrInfo.project_id);
        if (!requiredBranch || mr.mrInfo.target_branch !== requiredBranch) {
          this.logger.info(
            `[Batch Mr] [Validation] MR Group for "${mrInfo.mrInfo.title}" is invalid due to MR "${mr.mrInfo.title}" (id: ${mr.mr_id}). ` +
              `Project: ${mr.mrInfo.project_id}, Target Branch: ${mr.mrInfo.target_branch}. ` +
              `Required branch is "${requiredBranch ?? 'not in main repos'}". Skipping entire group.`,
          );
          return false;
        }
        return true;
      });

      if (isGroupValid) {
        validMrInfos.push(mrInfo);
      }
    }
    this.logger.info(`[Batch Mr] Validation complete. Found ${validMrInfos.length} valid MRs.`);
    return validMrInfos;
  }
  // 发起Batch Mr
  async createBatchMr(mrInfos: MrReviewerInfo[], busBatchInfo: BusBatchInfoTable, version: string) {
    this.logger.info(`[Batch Mr] start createBatchMr`);
    // 仅限7仓，筛选传入的mr，其余逻辑与之前相同
    const validMrInfos = await this.filterAndValidateMrs(mrInfos);
    let allMrInfos: MrReviewerInfo[] = validMrInfos;
    try {
      const allRelationMrPromise = validMrInfos.map(async mrInfo => await this.bits.getMrRelationList(mrInfo.mr_id));
      const allRelationMr = await Promise.all(allRelationMrPromise);
      this.logger.info(`[Batch Mr] all relation mr : ${allRelationMr.map(mr => mr?.length)}`);
      if (allRelationMr) {
        for (const relationMrs of allRelationMr) {
          if (relationMrs) {
            const relationMrInfos = await this.bits.getMrInfoAndReviewer(relationMrs.map(relationMr => relationMr.id));
            allMrInfos = allMrInfos.concat(relationMrInfos);
          }
        }
      }
    } catch (e) {
      this.logger.info(`[Batch Mr] get mr relation list failed: ${e}`);
    }
    this.logger.info(`[Batch Mr] start define branch`);
    const batchMrBranchName = `p/batch_mr/auto_create_${version.toString().split('.').join('_')}_batch_${nanoid(6)}_count_${validMrInfos.length}`;
    this.logger.info(`[Batch Mr] start record repo`);
    const repoMap: { [key: number]: string } = {};
    const mrRepos: MrRepos[] = [];
    for (const mrInfo of allMrInfos) {
      const projInfo = repos.searchProjectInfo({ projectId: mrInfo.mrInfo.project_id });
      if (!projInfo) {
        continue;
      }
      if (repoMap[mrInfo.mrInfo.project_id]) {
        continue;
      }
      const createBranchRet = await this.gitlab.createBranch(
        mrInfo.mrInfo.project_id,
        batchMrBranchName,
        mrInfo.mrInfo.target_branch,
      );
      repoMap[mrInfo.mrInfo.project_id] = mrInfo.mrInfo.target_branch;
      if (createBranchRet.code === NetworkCode.Error) {
        throw new Error(createBranchRet.message);
      }
      const mainRepos = repos.main_repos;
      mrRepos.push({
        projectId: mrInfo.mrInfo.project_id,
        sourcesBranch: batchMrBranchName,
        targetBranch: mrInfo.mrInfo.target_branch,
        isHost: mainRepos.find(it => it.projectId === mrInfo.mrInfo.project_id) !== undefined,
        platform: projInfo.platform,
      });
      await this.gitlab.protectBranch(mrInfo.mrInfo.project_id, batchMrBranchName);
    }
    busBatchInfo.batchBranchName = batchMrBranchName;
    busBatchInfo.mrRepos = mrRepos;
    await this.busBatchModel.updateOne(
      {
        batchKey: busBatchInfo.batchKey,
      },
      busBatchInfo,
    );
    for (const mrInfo of validMrInfos) {
      if (mrInfo.mrInfo.title.includes('封版占位MR')) {
        continue;
      }
      await this.mergeSubMr(mrInfo.mrInfo, batchMrBranchName, busBatchInfo, version);
    }
  }

  // 补偿同步子Mr代码
  async syncSubMrs(version?: string) {
    this.logger.info(`[Batch Mr] [syncSubMrs] start sync sub mrs`);
    // const currentBus = await this.busDao.queryCurrentIntegrationBus();
    // if (!currentBus) {
    //   this.logger.info(`[Batch Mr] [syncSubMrs] failed: no bus`);
    //   return;
    // }
    const busBatchInfo = await this.busBatchModel
      .findOne({
        batchStatus: BusBatchStatus.OnGoing,
        version: version ?? (await this.busVersion()),
      })
      .exec();
    if (!busBatchInfo || !busBatchInfo.batchBranchName) {
      this.logger.info(`[Batch Mr] [syncSubMrs] failed: no batch info/branch`);
      return;
    }
    const batchSubMrs: BatchSubMrInfoTable[] = await this.batchSubMrInfoModel.find({
      batchKey: busBatchInfo.batchKey,
    });
    for (const batchSubMr of batchSubMrs) {
      if (
        batchSubMr.status === BatchSubMrStatus.Wating ||
        batchSubMr.status === BatchSubMrStatus.Merged ||
        batchSubMr.status === BatchSubMrStatus.Abandoned
      ) {
        continue;
      }
      const mrInfo = await this.bits.getMrInfo({ mrId: batchSubMr.originMrId });
      if (mrInfo) {
        if (mrInfo.state === MrState.merged || mrInfo.state === MrState.forceMerged) {
          batchSubMr.status = BatchSubMrStatus.Merged;
          await this.batchSubMrInfoModel.updateOne(
            {
              batchKey: busBatchInfo.batchKey,
              originMrId: mrInfo.id,
            },
            batchSubMr,
          );
          continue;
        }
        this.logger.info(`[Batch Mr] [syncSubMrs] start sync: ${mrInfo.title}`);
        try {
          await this.mergeSubMr(mrInfo, busBatchInfo.batchBranchName, busBatchInfo, await this.busVersion());
        } catch (e) {
          this.logger.info(`[Batch Mr] [syncSubMrs] exception ${e}`);
        }
      }
    }
  }

  async mergeSubMr(mrInfo: MrInfo, batchMrBranch: string, busBatchInfo: BusBatchInfoTable, version: string) {
    const allRelationMrs = await this.bits.getMrRelationList(mrInfo.id);
    let allMrs = [mrInfo];
    if (allRelationMrs) {
      const relationMrInfos = await this.bits.fetchAllMrInfo(allRelationMrs.map(relationMr => relationMr.id));
      allMrs = allMrs.concat(relationMrInfos);
    }
    // 创建待并入Batch的中间分支
    let batchSubMrInfo: BatchSubMrInfoTable | null = await this.batchSubMrInfoModel.findOne({
      batchKey: busBatchInfo.batchKey,
      originMrId: mrInfo.id,
    });
    if (!batchSubMrInfo) {
      batchSubMrInfo = {} as BatchSubMrInfoTable;
      batchSubMrInfo.batchKey = busBatchInfo.batchKey;
      batchSubMrInfo.busId = 'off_line_bus';
      batchSubMrInfo.version = version;
      batchSubMrInfo.status = BatchSubMrStatus.Wating;
      batchSubMrInfo.originMrId = mrInfo.id;
      batchSubMrInfo.relationMiddleMrs = [];
      batchSubMrInfo.mrInfo = mrInfo;
      await this.batchSubMrInfoModel.create(batchSubMrInfo);
    }
    const batchRelationMiddleMrInfo: BatchRelationMiddleMr[] = [];
    const middleBatchBranchName = `p/batch_mr/middle_branch/auto_create_${mrInfo.from_branch.toString().split('/').join('_')}_from_${mrInfo.id}_${nanoid(6)}`;
    let middlewareBranchName = '';
    for (const mr of allMrs) {
      const hasDiff = await this.checkHasDiff(mr.project_id, batchMrBranch, mr.from_branch);
      this.logger.info(
        `[Batch Mr] [mergeSubMr] mr ${mr.title} no diff from ${mr.from_branch} to ${mr.target_branch} in repo ${mr.project_id}`,
      );
      if (mr.project_id === 134421) {
        middlewareBranchName = mr.from_branch;
      }
      if (!hasDiff) {
        continue;
      }
      const createBranchRet = await this.gitlab.createBranch(mr.project_id, middleBatchBranchName, mr.from_branch);
      const gitlabMr = await this.gitlab.createMr(
        mr.project_id,
        middleBatchBranchName,
        batchMrBranch,
        `Auto: LV-Batch-MiddleMr:${mr.from_branch}_in_project_${mr.project_name}_${nanoid(6)}`,
      );
      batchRelationMiddleMrInfo.push({
        projectId: mr.project_id,
        middleMrId: gitlabMr.id,
        middleMrIid: gitlabMr.iid,
        middleBranch: middleBatchBranchName,
        status:
          gitlabMr.merge_status === 'can_be_merged'
            ? BatchSubMrRelatedMrStatus.Waiting
            : BatchSubMrRelatedMrStatus.Conflicting,
        middleMrUrl: gitlabMr.web_url,
        originBranch: mr.from_branch, // 保存原分支信息
      } as BatchRelationMiddleMr);
    }
    this.logger.info(`[Batch Mr] [mergeSubMr] batchRelationMiddleMrInfo: ${JSON.stringify(batchRelationMiddleMrInfo)}`);
    if (batchRelationMiddleMrInfo && batchRelationMiddleMrInfo.length > 0) {
      batchSubMrInfo.relationMiddleMrs = batchRelationMiddleMrInfo;
      batchSubMrInfo.status = BatchSubMrStatus.Wating;
    }
    const protocolId = await this.getProtocolId(middlewareBranchName);
    if (protocolId) {
      batchSubMrInfo.protocolId = Number(protocolId);
    }
    await this.batchSubMrInfoModel.updateOne(
      {
        batchKey: busBatchInfo.batchKey,
        originMrId: mrInfo.id,
      },
      batchSubMrInfo,
    );
    return batchSubMrInfo;
  }

  // 同步中间分支与原分支
  async syncMiddleBranchWithOrigin(
    subMr: BatchSubMrInfoTable,
    mrInfo: MrInfo,
  ): Promise<{ success: boolean; message: string; hasConflict: boolean; conflictMrs: GitlabMrResponse[] }> {
    this.logger.info(
      `[Batch Mr] [syncMiddleBranchWithOrigin] start syncing middle branches with origin for MR ${mrInfo.id}`,
    );

    const conflictMrs: GitlabMrResponse[] = [];
    let hasAnyConflict = false;

    // 为每个中间分支同步原分支的最新代码
    for (let i = 0; i < subMr.relationMiddleMrs.length; i++) {
      const middleMr = subMr.relationMiddleMrs[i];

      // 使用保存的原分支信息
      if (!middleMr.originBranch) {
        this.logger.warn(
          `[Batch Mr] [syncMiddleBranchWithOrigin] no origin branch info for project ${middleMr.projectId}`,
        );
        continue;
      }

      try {
        // 检查原分支是否有新的提交
        const hasDiff = await this.checkHasDiff(middleMr.projectId, middleMr.middleBranch, middleMr.originBranch);

        if (hasDiff) {
          this.logger.info(
            `[Batch Mr] [syncMiddleBranchWithOrigin] syncing middle branch ${middleMr.middleBranch} with origin ${middleMr.originBranch} in project ${middleMr.projectId}`,
          );

          // 创建合并请求将原分支合并到中间分支
          const syncMr = await this.gitlab.createMr(
            middleMr.projectId,
            middleMr.originBranch, // 源分支：原分支
            middleMr.middleBranch, // 目标分支：中间分支
            `Auto: Sync ${middleMr.originBranch} into middle branch ${middleMr.middleBranch}`,
          );

          if (syncMr && syncMr.merge_status === 'can_be_merged') {
            // 如果可以自动合并，则执行合并
            await this.gitlab.acceptMr(middleMr.projectId, syncMr.iid);
            this.logger.info(
              `[Batch Mr] [syncMiddleBranchWithOrigin] successfully synced ${middleMr.originBranch} into middle branch`,
            );
          } else {
            // 有冲突，需要手动解决
            this.logger.warn(
              `[Batch Mr] [syncMiddleBranchWithOrigin] conflict detected when syncing ${middleMr.originBranch} into middle branch`,
            );
            hasAnyConflict = true;

            // 更新中间分支状态为冲突
            middleMr.status = BatchSubMrRelatedMrStatus.Conflicting;
            conflictMrs.push(syncMr);
          }
        } else {
          this.logger.info(
            `[Batch Mr] [syncMiddleBranchWithOrigin] middle branch ${middleMr.middleBranch} is already up to date with origin ${middleMr.originBranch}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `[Batch Mr] [syncMiddleBranchWithOrigin] failed to sync project ${middleMr.projectId}: ${error}`,
        );
        return {
          success: false,
          message: `Failed to sync project ${middleMr.projectId}: ${error}`,
          hasConflict: false,
          conflictMrs: [],
        };
      }
    }

    // 更新数据库中的中间分支状态
    if (conflictMrs.length > 0) {
      await this.batchSubMrInfoModel.updateOne(
        {
          batchKey: subMr.batchKey,
          originMrId: subMr.originMrId,
        },
        subMr,
      );
    }

    return {
      success: !hasAnyConflict,
      message: hasAnyConflict
        ? 'Some middle branches have conflicts after sync'
        : 'All middle branches synced successfully',
      hasConflict: hasAnyConflict,
      conflictMrs,
    };
  }
  // 处理中间分支同步冲突，应该不会调用
  async handleMiddleBranchSyncConflict(
    subMr: BatchSubMrInfoTable,
    busBatchInfo: BusBatchInfoTable,
    version: string,
    conflictMrs: GitlabMrResponse[],
  ) {
    this.logger.info(`[Batch Mr] [handleMiddleBranchSyncConflict] handling sync conflicts for MR ${subMr.originMrId}`);

    const mrInfo = await this.bits.getMrInfo({ mrId: subMr.originMrId });
    if (!mrInfo) {
      return;
    }

    // 发送冲突通知卡片
    await this.lark.sendCardMessage(
      UserIdType.chatId,
      busBatchInfo.chatId,
      this.batchMrCard.buildBatchSubmrSyncConflictCard(mrInfo, subMr, busBatchInfo, version, conflictMrs),
    );
  }

  // 创建Batch Mr群
  async createBatchGroup(version: string, subMrs: MrReviewerInfo[], busBatchInfo: BusBatchInfoTable) {
    const versionBatchInfos: BusBatchInfoTable[] = await this.busBatchModel.find({
      version,
    });
    const versionInfo = await this.versionProcessInfoDao.getCurrentVersionProcessInfo(177501, version);
    const bms = await this.queryBothBM(version);
    const bmIds = values(bms)
      .flat()
      .map(it => it.openId);
    const managerEmails = [
      '<EMAIL>',
      // '<EMAIL>',
      // '<EMAIL>',
      // '<EMAIL>',
      // '<EMAIL>',
    ];
    if (versionInfo?.pcInfo?.bmInfo?.[BmType.rd].email) {
      managerEmails.push(versionInfo?.pcInfo?.bmInfo?.[BmType.rd].email ?? '');
    }
    const managerUsers = await this.lark.searchUserInfoBatch(managerEmails);
    bmIds.push(...managerUsers.map(user => user.open_id));

    // Batch Mr Mater: liujinquan
    const batchMrMaster = await this.lark.searchUserInfoByEmail('<EMAIL>');
    if (!batchMrMaster) {
      this.logger.info(`[Batch Mr] fail to find master , return`);
      return null;
    }

    const subMrAuthorIds = await this.getMrAuthorIds(subMrs.map(subMr => subMr.mrInfo.author));

    let chatID = versionBatchInfos[0] ? versionBatchInfos[0].chatId : undefined;

    if (!chatID) {
      const chat = await this.lark.createLarkGroup(
        {
          user_id_type: UserIdType.openId,
          set_bot_manager: false,
        },
        {
          description: '',
          name: `剪映/CapCut ${version} 版本 - Batch Mr群`,
          owner_id: batchMrMaster.open_id,
          user_id_list: compact(subMrs.map(subMr => subMrAuthorIds[subMr.mrInfo.author])).concat(bmIds),
        },
      );
      chatID = chat.chat_id;
    }
    // 落库
    // const chatID = await this.lark.chatId2OpenChatId('7399995177920610307');
    try {
      await this.lark.addUserToChatGroup(
        chatID,
        UserIdType.openId,
        compact(subMrs.map(subMr => subMrAuthorIds[subMr.mrInfo.author])).concat(bmIds),
      );
    } catch (e) {
      throw e;
    }
    busBatchInfo.chatId = chatID;
    await this.busBatchModel.create(busBatchInfo);

    // 发通知
    await this.lark.sendCardMessage(
      UserIdType.chatId,
      chatID,
      this.batchMrCard.buildBatchMrNoticeCard(version, subMrs, versionInfo),
    );
    this.logger.info(`[Batch Mr] trigger Batch, submrs: ${subMrs.map(mr => mr.mrInfo.title)}`);
    return busBatchInfo;
  }

  // 冲突处理
  async pushResoveSubMrConflict(batchSubMrInfo: BatchSubMrInfoTable, busBatchInfo: BusBatchInfoTable, version: string) {
    const mrInfo = await this.bits.getMrInfo({ mrId: batchSubMrInfo.originMrId });
    if (!mrInfo) {
      return;
    }
    const newMiddleMrPromises = batchSubMrInfo.relationMiddleMrs.map(async middleMr => {
      const mrStatus = await this.gitlab.getMrStatus(middleMr.projectId, middleMr.middleMrIid);
      middleMr.status =
        mrStatus.merge_status === 'can_be_merged'
          ? BatchSubMrRelatedMrStatus.Waiting
          : BatchSubMrRelatedMrStatus.Conflicting;
      return middleMr;
    });
    batchSubMrInfo.relationMiddleMrs = await Promise.all(newMiddleMrPromises);
    await this.batchSubMrInfoModel.updateOne(
      {
        batchKey: batchSubMrInfo.batchKey,
        originMrId: batchSubMrInfo.originMrId,
      },
      batchSubMrInfo,
    );

    await this.lark.sendCardMessage(
      UserIdType.chatId,
      busBatchInfo.chatId,
      this.batchMrCard.buildBatchSubmrConflictCard(mrInfo, batchSubMrInfo, busBatchInfo, version),
    );
  }

  // 冲突解决回调
  async subMrConflictResolvedCardHandler(data: CardCallback) {
    const { mrId, busId } = data.action.value;
    await this.subMrConflictResolved(Number(mrId));
    return undefined;
  }
  async subMrConflictResolved(mrId: number) {
    // const currentBus = await this.busDao.queryCurrentIntegrationBus();
    // if (!currentBus) {
    //   return;
    // }
    const busBatchInfo: BusBatchInfo | null = await this.busBatchModel.findOne({
      batchStatus: BusBatchStatus.OnGoing,
      version: await this.busVersion(),
    });
    if (!busBatchInfo) {
      return;
    }
    const batchSubMrInfo: BatchSubMrInfoTable | null = await this.batchSubMrInfoModel.findOne({
      originMrId: mrId,
      batchKey: busBatchInfo.batchKey,
    });
    if (!batchSubMrInfo) {
      return;
    }
    await this.checkSpecificSubMr(mrId, busBatchInfo.batchKey, busBatchInfo.version);
  }

  async mergeBatchMrTarget(batchInfo: BusBatchInfoTable) {
    const mrInfo = await this.bits.getMrInfo({ mrId: batchInfo.batchMrId });
    if (!mrInfo) {
      return;
    }
    const hostHasDiff = await this.checkHasDiff(mrInfo.project_id, mrInfo.from_branch, mrInfo.target_branch);
    const mergeTargetMidBranch = `p/batch_mr/merge_target/auto_create_${mrInfo.from_branch.toString().split('/').join('_')}_from_${mrInfo.id}_${nanoid(6)}`;
    if (hostHasDiff) {
      const createBranchRet = await this.gitlab.createBranch(
        mrInfo.project_id,
        mergeTargetMidBranch,
        mrInfo.target_branch,
      );
      const gitlabMr = await this.gitlab.createMr(
        mrInfo.project_id,
        mergeTargetMidBranch,
        mrInfo.from_branch,
        `Auto: LV-Batch-Mr:${mrInfo.from_branch}_merge_target`,
      );
      await this.gitlab.mergeV2(mrInfo.project_id, gitlabMr.iid, true);
    }
    const relationMrs = await this.bits.getMrRelationList(batchInfo.batchMrId);
    if (!relationMrs) {
      return;
    }
    for (const relationMr of relationMrs) {
      const relationHasDiff = await this.checkHasDiff(
        relationMr.project_id,
        relationMr.from_branch,
        relationMr.target_branch,
      );
      if (relationHasDiff) {
        const createBranchRet = await this.gitlab.createBranch(
          relationMr.project_id,
          mergeTargetMidBranch,
          relationMr.target_branch,
        );
        const gitlabMr = await this.gitlab.createMr(
          relationMr.project_id,
          mergeTargetMidBranch,
          relationMr.from_branch,
          `Auto: LV-Batch-Mr:${mrInfo.from_branch}_merge_target`,
        );
        await this.gitlab.mergeV2(relationMr.project_id, gitlabMr.iid, true);
      }
    }
  }

  async checkSpecificSubMr(subMrId: number, batchKey: string, version: string) {
    const mrInfo = await this.bits.getMrInfo({ mrId: subMrId });
    if (!mrInfo) {
      return;
    }
    const currentBatchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      batchStatus: BusBatchStatus.OnGoing,
      batchKey,
    });
    if (!currentBatchInfo) {
      return;
    }
    const batchSubMrInfo: BatchSubMrInfoTable | null = await this.batchSubMrInfoModel.findOne({
      originMrId: subMrId,
      batchKey: currentBatchInfo.batchKey,
    });
    if (!batchSubMrInfo) {
      return;
    }
    const checkedSubMrInfo = await this.checkBatchSubMrsStatus(batchSubMrInfo);
    const conflictRelationMrs = checkedSubMrInfo.relationMiddleMrs.filter(
      middleMr => middleMr.status === BatchSubMrRelatedMrStatus.Conflicting,
    );
    if (conflictRelationMrs.length <= 0) {
      await this.mergeBatchMrTarget(currentBatchInfo);
      await this.mergeBatchSubMr(checkedSubMrInfo, mrInfo, await this.busVersion(), currentBatchInfo);
      if (checkedSubMrInfo.protocolId && !currentBatchInfo.protocolIds.includes(checkedSubMrInfo.protocolId)) {
        currentBatchInfo.protocolIds.push(checkedSubMrInfo.protocolId);
        await this.busBatchModel.updateOne(
          {
            version: currentBatchInfo.version,
            batchKey: currentBatchInfo.batchKey,
          },
          currentBatchInfo,
        );
      }
    } else if (conflictRelationMrs.length > 0) {
      await this.pushResoveSubMrConflict(checkedSubMrInfo, currentBatchInfo, version);
    }
  }

  async checkWaitingMr(needPushResolveConflict = false, batchVersion?: string) {
    // const currentBus = await this.busDao.queryCurrentIntegrationBus();
    // if (!currentBus) {
    //   return;
    // }
    const currentBatchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      version: batchVersion ?? (await this.busVersion()),
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (!currentBatchInfo) {
      return;
    }
    const unmergedSubMrInfos: BatchSubMrInfoTable[] = await this.batchSubMrInfoModel.find({
      batchKey: currentBatchInfo.batchKey,
      status: BatchSubMrStatus.Wating,
    });
    for (const unmergedSubMr of unmergedSubMrInfos) {
      const checkedSubMrInfo = await this.checkBatchSubMrsStatus(unmergedSubMr);
      const conflictRelationMrs = checkedSubMrInfo.relationMiddleMrs.filter(
        middleMr => middleMr.status === BatchSubMrRelatedMrStatus.Conflicting,
      );
      if (conflictRelationMrs.length <= 0) {
        const mrInfo = await this.bits.getMrInfo({ mrId: checkedSubMrInfo.originMrId });
        await this.mergeBatchMrTarget(currentBatchInfo);
        await this.mergeBatchSubMr(checkedSubMrInfo, mrInfo, await this.busVersion(), currentBatchInfo);
        if (unmergedSubMr.protocolId && !currentBatchInfo.protocolIds.includes(unmergedSubMr.protocolId)) {
          currentBatchInfo.protocolIds.push(unmergedSubMr.protocolId);
          await this.busBatchModel.updateOne(
            {
              version: currentBatchInfo.version,
              batchKey: currentBatchInfo.batchKey,
            },
            currentBatchInfo,
          );
        }
      } else if (conflictRelationMrs.length > 0 && needPushResolveConflict) {
        await this.pushResoveSubMrConflict(
          checkedSubMrInfo,
          currentBatchInfo,
          batchVersion ?? (await this.busVersion()),
        );
      }
    }
    if (!currentBatchInfo.batchMrId) {
      await this.launchBatchMrMerge(true, batchVersion);
    }
  }

  async sendOnBatchMrs() {
    const currentBatchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      version: await this.busVersion(),
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (!currentBatchInfo) {
      return;
    }
    const unmergedSubMrInfos: BatchSubMrInfoTable[] = await this.batchSubMrInfoModel.find({
      batchKey: currentBatchInfo.batchKey,
      status: BatchSubMrStatus.OnBatch,
    });
    const mrInfos = await Promise.all(
      unmergedSubMrInfos.map(async it => await this.bits.getMrInfo({ mrId: it.originMrId })),
    );
    const unMergedInfos: MrInfo[] = mrInfos.filter((info): info is MrInfo => info?.state === MrState.opened);

    await this.lark.sendCardMessage(
      UserIdType.chatId,
      currentBatchInfo.chatId,
      this.batchMrCard.buildBatchMrInfoCard(unMergedInfos),
    );
  }

  // 判断是否需要加入Batch
  // 比如Pipeline已经跑完，马上就要合入的，不需要加入batch，返回true
  async shouldAvoidBatch(mrInfo: MrReviewerInfo) {
    return false;
  }

  // Batch MR 相关数据上报
  async batchMRDataReport(reportInfo: BatchMRReportInfo) {
    // 写入数据库
    await this.batchMRReportInfoDao.create(reportInfo);
  }

  // bits mr合入回调
  async handleMrMerged(mrInfo: MrInfo) {
    // 上报 Batch MR 合入（先简单处理下，通过 MR 标题来判断）
    if (mrInfo.title.includes('LV-Batch-Mr:') && mrInfo.author === 'liujinquan') {
      // 判断为 Batch MR，做一下合入上报
      await this.batchMRDataReport({
        type: BatchMRReportType.BatchMRMerged,
        timestamp: Date.now(),
        batchMRId: mrInfo.id,
        batchMRBranchName: mrInfo.from_branch,
        busVersion: await this.busVersion(),
      });
    }
    // const switchOn = await this.isBatchMrSwitchOn();
    // if (!switchOn) {
    //   this.logger.info(`[Batch Mr] return because switch off`);
    //   return;
    // }
    // if (await this.checkBatchMrCompleted(mrInfo, BusBatchStatus.Merged)) {
    //   return;
    // }
    // await this.cancelBatch(mrInfo, '本体已合入', true);
  }
  // bits mr关闭回调
  async handleMrClosed(mrInfo: MrInfo) {
    // const switchOn = await this.isBatchMrSwitchOn();
    // if (!switchOn) {
    //   this.logger.info(`[Batch Mr] return because switch off`);
    //   return;
    // }
    // if (await this.checkBatchMrCompleted(mrInfo, BusBatchStatus.Abandoned)) {
    //   return;
    // }
    // await this.cancelBatch(mrInfo, '本体已关闭', false);
  }
  // 将Mr从Batch Mr移除
  async cancelBatch(mrInfo: MrInfo, reason: string, merged: boolean) {
    this.logger.info(`[Batch Mr] start cancel batch, reason : ${reason}`);
    // const currentBus = await this.busDao.queryCurrentIntegrationBus();
    // if (!currentBus) {
    //   this.logger.info(`[Batch Mr] cancel Batch for mr id: ${mrInfo.id} , name : ${mrInfo.title} failed, no bus`);
    //   return;
    // }
    const currentBatchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      version: await this.busVersion(),
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (!currentBatchInfo) {
      this.logger.info(
        `[Batch Mr] cancel Batch for mr id: ${mrInfo.id} , name : ${mrInfo.title} failed, no current Batch`,
      );
      return;
    }

    const batchSubMrInfo: BatchSubMrInfoTable | null = await this.batchSubMrInfoModel.findOne({
      version: await this.busVersion(),
      batchKey: currentBatchInfo.batchKey,
      originMrId: mrInfo.id,
    });
    if (!batchSubMrInfo) {
      this.logger.info(
        `[Batch Mr] cancel Batch for mr id: ${mrInfo.id} , name : ${mrInfo.title} failed, not batch submr`,
      );
      return;
    }
    if (batchSubMrInfo.status === BatchSubMrStatus.OnBatch && !merged) {
      await this.revertSubMrFromBatch(currentBatchInfo, batchSubMrInfo);
    }
    batchSubMrInfo.status = BatchSubMrStatus.Abandoned;
    await this.batchSubMrInfoModel.updateOne(
      {
        version: await this.busVersion(),
        batchKey: currentBatchInfo.batchKey,
        originMrId: mrInfo.id,
      },
      batchSubMrInfo,
    );
    this.logger.info(
      `[Batch Mr] cancel Batch for mr id: ${mrInfo.id} , name : ${mrInfo.title} suceed, reason: ${reason}`,
    );
    // await this.lark.sendCardMessage(
    //   UserIdType.chatId,
    //   currentBatchInfo.chatId,
    //   this.batchMrCard.buildCancelBatchCard(mrInfo, true, await this.busVersion(), reason),
    // );
  }

  // 更新submr状态
  async checkBatchSubMrsStatus(subMr: BatchSubMrInfoTable) {
    const newMiddleMrPromises = subMr.relationMiddleMrs.map(async middleMr => {
      const mrStatus = await this.gitlab.getMrStatus(middleMr.projectId, middleMr.middleMrIid);
      middleMr.status =
        mrStatus.merge_status === 'can_be_merged'
          ? BatchSubMrRelatedMrStatus.Waiting
          : BatchSubMrRelatedMrStatus.Conflicting;
      return middleMr;
    });
    subMr.relationMiddleMrs = await Promise.all(newMiddleMrPromises);
    await this.batchSubMrInfoModel.updateOne(
      {
        batchKey: subMr.batchKey,
        originMrId: subMr.originMrId,
      },
      subMr,
    );
    return subMr;
  }
  // 合入submr
  async mergeBatchSubMr(
    subMr: BatchSubMrInfoTable,
    mrInfo: MrInfo | undefined,
    version: string,
    busBatchInfo: BusBatchInfoTable,
  ) {
    if (!mrInfo) {
      this.logger.info(`[Batch Mr] merge batch sub mr for id ${subMr.originMrId} failed, no mr info`);
      return false;
    }
    if (
      subMr.relationMiddleMrs.filter(middleMr => middleMr.status === BatchSubMrRelatedMrStatus.Conflicting).length > 0
    ) {
      return false;
    }

    // 在合入前，先同步中间分支与原分支，确保代码最新
    const syncResult = await this.syncMiddleBranchWithOrigin(subMr, mrInfo);
    if (!syncResult.success) {
      this.logger.info(`[Batch Mr] sync middle branch with origin failed: ${syncResult.message}`);
      if (syncResult.hasConflict) {
        // 如果有冲突，更新状态并发送通知
        await this.handleMiddleBranchSyncConflict(subMr, busBatchInfo, version, syncResult.conflictMrs);
        return false;
      }
      // 其他错误也返回失败
      return false;
    }

    for (let i = 0; i < subMr.relationMiddleMrs.length; i++) {
      const mergeRes = await this.gitlab.mergeV2(
        subMr.relationMiddleMrs[i].projectId,
        subMr.relationMiddleMrs[i].middleMrIid,
      );
      subMr.relationMiddleMrs[i].mergeCommitSha = mergeRes.merge_commit_sha;
      if ((mergeRes.merge_commit_sha?.length ?? 0) <= 1) {
        await this.lark.sendCardMessage(
          UserIdType.chatId,
          busBatchInfo.chatId,
          this.batchMrCard.buildSubMrMergeIssueCard(mrInfo, subMr.relationMiddleMrs[i].middleMrUrl),
        );
      } else {
        subMr.relationMiddleMrs[i].status = BatchSubMrRelatedMrStatus.OnBatch;
      }
    }
    subMr.status = BatchSubMrStatus.OnBatch;
    await this.batchSubMrInfoModel.updateOne(
      {
        batchKey: subMr.batchKey,
        originMrId: subMr.originMrId,
      },
      subMr,
    );
    await this.lark.sendCardMessage(
      UserIdType.chatId,
      busBatchInfo.chatId,
      this.batchMrCard.buildOnBatchCard(mrInfo, subMr, version),
    );
    // 子 MR 合入 Batch 上报
    await this.batchMRDataReport({
      type: BatchMRReportType.SubMRIntoBatch,
      timestamp: Date.now(),
      batchMRId: busBatchInfo.batchMrId,
      batchMRBranchName: busBatchInfo.batchBranchName,
      busVersion: busBatchInfo.version,
      subMRId: mrInfo.id,
      subMRTitle: mrInfo.title,
    });
    return true;
  }
  // 开始合入Batch Mr
  async launchBatchMrMerge(forceLaunch = false, batchVersion?: string) {
    const busBatchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      version: batchVersion ?? (await this.busVersion()),
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (!busBatchInfo || !busBatchInfo.batchBranchName) {
      return;
    }
    if (busBatchInfo.batchMrId) {
      return;
    }
    const batchSubMrs: BatchSubMrInfoTable[] | null = await this.batchSubMrInfoModel.find({
      version: await this.busVersion(),
      batchKey: busBatchInfo.batchKey,
    });
    if (!batchSubMrs) {
      // 废弃Batch
      return;
    }
    const waitingSubMrs = batchSubMrs.filter(batchSubMr => batchSubMr.status === BatchSubMrStatus.Wating);
    const batchMrMaster = await this.lark.searchUserInfoByEmail('<EMAIL>');
    if (waitingSubMrs.length > 0 && !forceLaunch) {
      await this.lark.sendTextMessage(UserIdType.openId, batchMrMaster?.open_id ?? '', '未能创建Batch Mr-有子Mr未合入');
      return;
    }

    const mrParams = await this.bitsConfigV2Service.buildMrConfig({
      title: `LV-Batch-Mr: ${busBatchInfo.batchKey}`,
      repos: busBatchInfo.mrRepos,
      targetVersion: await this.busVersion(),
      author: 'liujinquan',
      type: MrType.merge,
      customFields: {
        range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
        how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
        the_test_results: 1,
        disable_compress_ld_for_inhouse: true, // iOS 屏蔽压缩链接器
        MR_FORBID_PACKAGE_CHECK: true, // iOS 屏蔽包大小检测
      },
      wip: false,
      review_start_type: 1,
    });
    mrParams.mr_reviewers = [];
    mrParams.review_fetch_mode = 2;
    mrParams.group_name = 'LV-iOS';
    await this.lark.sendTextMessage(
      UserIdType.openId,
      batchMrMaster?.open_id ?? '',
      `开始创建Batch Mr: ${busBatchInfo.batchKey}, [diffParams]: ${JSON.stringify(mrParams)}`,
    );
    const res = await this.bits.createMr(mrParams, 'liujinquan');
    if (res.code === 0 && res.data) {
      const mrInfo: MrInfo | undefined = await this.bits.getMrInfo({
        mrId: res.data.mr_id,
      });
      if (mrInfo) {
        busBatchInfo.batchMrId = mrInfo.id;
        busBatchInfo.batchMrInfo = mrInfo;
        await this.busBatchModel.updateOne(
          {
            batchKey: busBatchInfo.batchKey,
          },
          busBatchInfo,
        );
        const onBatchSubMrs = batchSubMrs.filter(subMr => subMr.status === BatchSubMrStatus.OnBatch);
        const originMrs = await Promise.all(
          onBatchSubMrs.map(async subMr => await this.bits.getMrInfo({ mrId: subMr.originMrId })),
        );
        const finalSubMrs: MrInfo[] = [];
        originMrs.forEach(originMr => {
          if (originMr) {
            finalSubMrs.push(originMr);
          }
        });
        await this.lark.sendCardMessage(
          UserIdType.chatId,
          busBatchInfo.chatId,
          this.batchMrCard.buildBatchMrLaunchCard(mrInfo, await this.busVersion(), finalSubMrs),
        );
        // 创建 Batch MR 成功，进行数据上报
        await this.batchMRDataReport({
          type: BatchMRReportType.CreateBatchMR,
          timestamp: Date.now(),
          batchMRId: mrInfo.id,
          batchMRBranchName: mrInfo.from_branch,
          busVersion: batchVersion ?? (await this.busVersion()),
        });
      }
    }

    await this.lark.sendTextMessage(
      UserIdType.openId,
      batchMrMaster?.open_id ?? '',
      `Batch Mr: ${busBatchInfo.batchKey}, [res]: ${JSON.stringify(res)}`,
    );
  }

  // 判断Batch Mr合入完成/废弃
  async checkBatchMrCompleted(mrInfo: MrInfo, status: BusBatchStatus) {
    const currentBusBatchInfo = await this.busBatchModel.findOne({
      version: '',
      batchMrId: mrInfo.id,
    });
    if (!currentBusBatchInfo) {
      return false;
    }
    currentBusBatchInfo.batchStatus = status;
    await this.busBatchModel.updateOne({
      version: '',
      batchMrId: mrInfo.id,
    });
    // await this.lark.sendCardMessage(
    //   UserIdType.chatId,
    //   currentBusBatchInfo.chatId,
    //   this.batchMrCard.buildBatchMrCompletedCard(mrInfo, currentBus, status === BusBatchStatus.Merged),
    // );
    return true;
  }

  async revertSubMrFromBatch(batchInfo: BusBatchInfoTable, subMrInfo: BatchSubMrInfoTable) {
    const failedGitlabMr: string[] = [];
    for (let i = 0; i < subMrInfo.relationMiddleMrs.length; i++) {
      if (subMrInfo.relationMiddleMrs[i].status === BatchSubMrRelatedMrStatus.OnBatch) {
        const res = await this.gitlab.revert(
          subMrInfo.relationMiddleMrs[i].projectId,
          subMrInfo.relationMiddleMrs[i].mergeCommitSha,
          batchInfo.batchBranchName,
        );
        if (!res) {
          failedGitlabMr.push(subMrInfo.relationMiddleMrs[i].middleMrUrl);
        }
        subMrInfo.relationMiddleMrs[i].status = BatchSubMrRelatedMrStatus.Waiting;
      }
    }
    if (failedGitlabMr.length > 0) {
      await this.lark.sendCardMessage(
        UserIdType.chatId,
        batchInfo.chatId,
        this.batchMrCard.buildSubMrRevertFailedCard(failedGitlabMr),
      );
    }
  }

  /**
   * 查询双端 BM ，已替换数据库
   * @param version 版本，如 12.4.0
   * @return key 是 PlatformType, value 是 BM 列表
   */
  async queryBothBM(version: string): Promise<Record<string, BuildMasterInfo[]>> {
    // 只查询LV的 app_id 列表
    const appIds = [AppSettingId.LV_ANDROID, AppSettingId.LV_IOS, AppSettingId.LV_WIN];

    const platformMap: Partial<Record<AppSettingId, PlatformType>> = {
      [AppSettingId.LV_ANDROID]: PlatformType.Android,
      [AppSettingId.LV_IOS]: PlatformType.iOS,
      [AppSettingId.LV_WIN]: PlatformType.PC,
    };

    return Promise.all(
      appIds.map(
        async (appId): Promise<[PlatformType | undefined, BuildMasterInfo[]]> => [
          platformMap[appId],
          await this.versionProcessInfoDao
            .getCurrentVersionProcessInfo(appId, version)
            .then(it => values(pick(it?.bmInfo, [BmType.rd, BmType.qa])))
            .then(it => it.filter(c => c).map(c => c!)),
        ],
      ),
    ).then(it => fromPairs(it));
  }

  /**
   * 返回给定的 MR 的 author 的 open id
   * @param mrAuthors
   */
  async getMrAuthorIds(mrAuthors: <AUTHORS>
    return mrAuthors.length <= 0
      ? {}
      : fromPairs(
          await this.lark
            .searchUserInfoBatch(mrAuthors)
            .then(it => it.map(u => [trim_suffix('@bytedance.com', u.email!), u.open_id])),
        );
  }

  // 检查分支有无diff
  async checkHasDiff(projectId: number, fromBranch: string, toBranch: string) {
    const ret = await this.gitlab.compare(`${projectId}`, fromBranch, toBranch, false);

    if (ret.code === NetworkCode.Error) {
      throw new Error(ret.message);
    }

    if (ret.data?.diffs && ret.data?.diffs.length > 0) {
      return true;
    }
    return false;
  }

  async cancelBatchMrProtect(version: string) {
    const batchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      version,
      batchStatus: BusBatchStatus.OnGoing,
    });
    if (!batchInfo) {
      return;
    }
    for (const mrRepo of batchInfo.mrRepos) {
      await this.gitlab.unProtectBranch(mrRepo.projectId, batchInfo.batchBranchName);
    }
  }

  // tcc开关
  async isBatchMrSwitchOn() {
    let batchMrSwitch = { enable: false };
    try {
      batchMrSwitch = JSON.parse(await this.tccClients.keys.get('batch_mr_switch')) as BatchMrSwitch;
    } catch (e) {
      this.logger.info(`[Batch Mr] get tcc config error`);
      return false;
    }
    return batchMrSwitch.enable;
  }

  async isBatchMr(mrId: number) {
    const batchInfo: BusBatchInfoTable | null = await this.busBatchModel.findOne({
      batchMrId: mrId,
    });
    if (batchInfo) {
      return true;
    }
    return false;
  }

  async busVersion() {
    const currentBus = await this.busDao.queryCurrentIntegrationBus();
    return currentBus?.version ?? '';
  }

  async apiTest() {
    // const mrStatus = await this.gitlab.getMrStatus(134421, 18915);
    // await this.gitlab.mergeV2(134421, 18915);
    // console.log(mrStatus.merge_status);
    await this.gitlab.unProtectBranch(134421, 'p/batch_mr/auto_create_15_5_0_batch_w68UOh_count_1');
    // await this.getProtocolId('feature/ai_sound');
  }

  async getProtocolId(branchName: string) {
    // https://lvdevops.bytedance.net/api/videoeditorBranch/id?branch=develop
    const request = new NetworkX('https://lvdevops.bytedance.net/api', {
      'Content-Type': 'application/json',
    });
    const protocolRes = await request.get(`/videoeditorBranch/id?branch=${branchName}`);
    console.log(protocolRes.id);
    return protocolRes.id;
  }
}
